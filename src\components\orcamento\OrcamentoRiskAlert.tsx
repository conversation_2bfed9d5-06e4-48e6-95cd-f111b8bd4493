/**
 * 🚨 Componente de Alerta de Risco Orçamentário
 * 
 * Exibe alertas visuais quando o orçamento informado pelo usuário
 * está significativamente abaixo da estimativa paramétrica da IA.
 * 
 * Casos de uso:
 * - Prevenção de projetos subdimensionados financeiramente
 * - Educação do usuário sobre custos reais de construção
 * - Sugestões inteligentes baseadas em IA/SINAPI
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { motion, AnimatePresence } from "framer-motion";
import {
  AlertTriangle,
  Calculator,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  DollarSign,
  Info,
  Lightbulb,
  TrendingUp,
  X
} from "lucide-react";
import React, { useState } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { formatCurrencyBR } from "@/lib/i18n";
import { cn } from "@/lib/utils";

import type { RiskAlert } from "@/hooks/useOrcamentoRiskAlert";

// ====================================
// 🎯 TIPOS E INTERFACES
// ====================================

export interface OrcamentoRiskAlertProps {
  riskAlert: RiskAlert;
  onAcceptSuggestion?: (suggestedAmount: number) => void;
  onDismiss?: () => void;
  className?: string;
  showDetails?: boolean;
}

// ====================================
// 🎨 CONFIGURAÇÕES VISUAIS POR NÍVEL DE RISCO
// ====================================

const getRiskConfig = (riskLevel: RiskAlert['riskLevel']) => {
  switch (riskLevel) {
    case 'low':
      return {
        bgColor: 'from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30',
        borderColor: 'border-green-200 dark:border-green-800',
        iconColor: 'text-green-600 dark:text-green-400',
        badgeColor: 'bg-green-500',
        icon: CheckCircle,
        title: 'Orçamento Adequado',
        priority: 'success' as const
      };
    case 'medium':
      return {
        bgColor: 'from-yellow-50 to-amber-50 dark:from-yellow-950/30 dark:to-amber-950/30',
        borderColor: 'border-yellow-200 dark:border-yellow-800',
        iconColor: 'text-yellow-600 dark:text-yellow-400',
        badgeColor: 'bg-yellow-500',
        icon: Info,
        title: 'Atenção Recomendada',
        priority: 'warning' as const
      };
    case 'high':
      return {
        bgColor: 'from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30',
        borderColor: 'border-orange-200 dark:border-orange-800',
        iconColor: 'text-orange-600 dark:text-orange-400',
        badgeColor: 'bg-orange-500',
        icon: AlertTriangle,
        title: 'Risco Alto',
        priority: 'danger' as const
      };
    case 'critical':
      return {
        bgColor: 'from-red-50 to-red-100 dark:from-red-950/50 dark:to-red-950/30',
        borderColor: 'border-red-300 dark:border-red-700',
        iconColor: 'text-red-600 dark:text-red-400',
        badgeColor: 'bg-red-500',
        icon: AlertTriangle,
        title: 'Crítico - Ação Necessária',
        priority: 'critical' as const
      };
    default:
      return {
        bgColor: 'from-slate-50 to-slate-100 dark:from-slate-950/30 dark:to-slate-900/30',
        borderColor: 'border-slate-200 dark:border-slate-800',
        iconColor: 'text-slate-600 dark:text-slate-400',
        badgeColor: 'bg-slate-500',
        icon: Info,
        title: 'Informação',
        priority: 'info' as const
      };
  }
};

// ====================================
// 🎯 COMPONENTE PRINCIPAL
// ====================================

export const OrcamentoRiskAlert: React.FC<OrcamentoRiskAlertProps> = ({
  riskAlert,
  onAcceptSuggestion,
  onDismiss,
  className,
  showDetails = true
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  // Não renderizar se não há risco e não está carregando
  if (!riskAlert.hasRisk && !riskAlert.isLoading && riskAlert.riskLevel === 'low') {
    return (
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        exit={{ opacity: 0, height: 0 }}
        transition={{ duration: 0.3 }}
        className={className}
      >
        <Alert className="border-green-200/70 bg-green-50/80 dark:border-green-800/50 dark:bg-green-950/30">
          <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertDescription className="text-green-800 dark:text-green-200">
            {riskAlert.message}
          </AlertDescription>
        </Alert>
      </motion.div>
    );
  }

  // Não renderizar se foi dispensado
  if (isDismissed) return null;

  // Estado de loading
  if (riskAlert.isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        transition={{ duration: 0.3 }}
        className={className}
      >
        <Alert className="border-blue-200/70 bg-blue-50/80 dark:border-blue-800/50 dark:bg-blue-950/30">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <AlertDescription className="text-blue-800 dark:text-blue-200">
              {riskAlert.message}
            </AlertDescription>
          </div>
        </Alert>
      </motion.div>
    );
  }

  // Estado de erro
  if (riskAlert.error) {
    return (
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        transition={{ duration: 0.3 }}
        className={className}
      >
        <Alert className="border-slate-200/70 bg-slate-50/80 dark:border-slate-800/50 dark:bg-slate-950/30">
          <Info className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          <AlertDescription className="text-slate-800 dark:text-slate-200">
            {riskAlert.message}
          </AlertDescription>
        </Alert>
      </motion.div>
    );
  }

  const config = getRiskConfig(riskAlert.riskLevel);
  const IconComponent = config.icon;

  const handleAcceptSuggestion = () => {
    if (onAcceptSuggestion) {
      onAcceptSuggestion(riskAlert.suggestedMinimum);
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    if (onDismiss) {
      onDismiss();
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, height: 0, y: -20 }}
        animate={{ opacity: 1, height: 'auto', y: 0 }}
        exit={{ opacity: 0, height: 0, y: -20 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
        className={cn("w-full", className)}
      >
        <Card className={cn(
          "relative overflow-hidden border-l-4",
          `bg-gradient-to-r ${config.bgColor}`,
          config.borderColor,
          "shadow-sm hover:shadow-md transition-all duration-300"
        )}>
          {/* Header */}
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1">
                <div className={cn(
                  "p-2 rounded-lg",
                  config.badgeColor
                )}>
                  <IconComponent className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <CardTitle className={cn("text-base font-semibold", config.iconColor)}>
                    {config.title}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {riskAlert.message}
                  </p>
                  {riskAlert.percentageDifference > 0 && (
                    <Badge className={cn("mt-2", config.badgeColor)}>
                      <TrendingUp className="h-3 w-3 mr-1" />
                      {riskAlert.percentageDifference.toFixed(1)}% acima da estimativa
                    </Badge>
                  )}
                </div>
              </div>
              
              {/* Botões de Ação */}
              <div className="flex items-center gap-2">
                {showDetails && riskAlert.hasRisk && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    {isExpanded ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                )}
                {onDismiss && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDismiss}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>

          {/* Conteúdo Expandível */}
          <AnimatePresence>
            {isExpanded && riskAlert.hasRisk && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <CardContent className="pt-0 space-y-4">
                  <Separator />
                  
                  {/* Detalhes da Análise */}
                  <div className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      {riskAlert.detailedMessage}
                    </p>
                    
                    {/* Comparação de Valores */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium">Seu Orçamento</span>
                        </div>
                        <p className="text-lg font-bold text-blue-600">
                          {formatCurrencyBR(riskAlert.userBudget)}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Calculator className="h-4 w-4 text-purple-500" />
                          <span className="text-sm font-medium">Estimativa IA</span>
                        </div>
                        <p className="text-lg font-bold text-purple-600">
                          {formatCurrencyBR(riskAlert.estimatedCost)}
                        </p>
                      </div>
                    </div>

                    {/* Sugestão */}
                    {riskAlert.riskLevel !== 'low' && (
                      <div className="bg-white/50 dark:bg-slate-900/50 p-4 rounded-lg border border-slate-200/50 dark:border-slate-700/50">
                        <div className="flex items-start gap-3">
                          <Lightbulb className="h-5 w-5 text-amber-500 mt-0.5" />
                          <div className="flex-1">
                            <h4 className="font-medium text-sm mb-1">Recomendação</h4>
                            <p className="text-sm text-muted-foreground mb-3">
                              Baseado em dados de mercado, recomendamos um orçamento mínimo de:
                            </p>
                            <p className="text-xl font-bold text-green-600 mb-3">
                              {formatCurrencyBR(riskAlert.suggestedMinimum)}
                            </p>
                            {onAcceptSuggestion && (
                              <Button
                                onClick={handleAcceptSuggestion}
                                size="sm"
                                className="bg-green-600 hover:bg-green-700 text-white"
                              >
                                <Calculator className="h-4 w-4 mr-2" />
                                Usar Sugestão da IA
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};

export default OrcamentoRiskAlert;