{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["mcp__supabase__list_projects", "mcp__supabase__list_migrations", "mcp__supabase__apply_migration", "mcp__supabase__list_edge_functions", "Bash(npm run deploy:edge-functions:*)", "Bash(supabase functions deploy:*)", "Bash(grep:*)", "mcp__supabase__execute_sql", "Bash(npx supabase functions deploy:*)", "Bash(npx supabase:*)", "mcp__supabase__deploy_edge_function", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx tsc:*)", "mcp__supabase__list_tables", "Bash(node:*)", "Bash(supabase status:*)", "mcp__supabase__get_anon_key", "Bash(npm run dev:*)", "Bash(rm:*)", "<PERSON><PERSON>(true)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["context7", "supabase", "fetch", "Sequential thinking", "postgres"]}