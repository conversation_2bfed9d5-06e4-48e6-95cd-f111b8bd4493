-- Migração para Sistema Híbrido de Despesas em Condomínios
-- Adiciona campos para suporte a despesas específicas por unidade
-- Data: 2025-01-20
-- Autor: <PERSON> Assistant

-- 1. <PERSON>riar ENUM para tipo de despesa
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_despesa_enum') THEN
        CREATE TYPE public.tipo_despesa_enum AS ENUM (
            'COMUM',      -- Despesa comum do condomínio (rateada)
            'ESPECIFICA'  -- Despesa específica de uma unidade
        );
    END IF;
END $$;

-- 2. Adicionar novos campos à tabela despesas
ALTER TABLE public.despesas 
ADD COLUMN IF NOT EXISTS tipo_despesa public.tipo_despesa_enum DEFAULT 'COMUM',
ADD COLUMN IF NOT EXISTS unidade_especifica_id uuid,
ADD COLUMN IF NOT EXISTS rateio_automatico boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS observacoes_unidade text;

-- 3. Adicionar comentários para documentação
COMMENT ON COLUMN public.despesas.tipo_despesa IS 'Tipo da despesa: COMUM (rateada entre unidades) ou ESPECIFICA (apenas uma unidade)';
COMMENT ON COLUMN public.despesas.unidade_especifica_id IS 'ID da unidade específica quando tipo_despesa = ESPECIFICA';
COMMENT ON COLUMN public.despesas.rateio_automatico IS 'Se a despesa deve ser rateada automaticamente (apenas para tipo COMUM)';
COMMENT ON COLUMN public.despesas.observacoes_unidade IS 'Observações específicas relacionadas à unidade';

-- 4. Adicionar constraint para garantir consistência
ALTER TABLE public.despesas 
ADD CONSTRAINT check_unidade_especifica_consistency 
CHECK (
    (tipo_despesa = 'COMUM' AND unidade_especifica_id IS NULL) OR
    (tipo_despesa = 'ESPECIFICA' AND unidade_especifica_id IS NOT NULL)
);

-- 5. Adicionar foreign key para unidade_especifica_id
ALTER TABLE public.despesas 
ADD CONSTRAINT fk_despesas_unidade_especifica 
FOREIGN KEY (unidade_especifica_id) 
REFERENCES public.obras(id) 
ON DELETE SET NULL;

-- 6. Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_despesas_tipo_despesa ON public.despesas(tipo_despesa);
CREATE INDEX IF NOT EXISTS idx_despesas_unidade_especifica ON public.despesas(unidade_especifica_id);
CREATE INDEX IF NOT EXISTS idx_despesas_obra_tipo ON public.despesas(obra_id, tipo_despesa);

-- 7. Atualizar dados existentes
-- Despesas de unidades de condomínio tornam-se específicas
UPDATE public.despesas 
SET 
    tipo_despesa = 'ESPECIFICA',
    unidade_especifica_id = obra_id,
    rateio_automatico = false
WHERE obra_id IN (
    SELECT id FROM public.obras 
    WHERE tipo_projeto = 'UNIDADE_CONDOMINIO'
);

-- 8. Atualizar obra_id das despesas específicas para apontar para o master
UPDATE public.despesas 
SET obra_id = (
    SELECT parent_obra_id 
    FROM public.obras 
    WHERE id = despesas.unidade_especifica_id
)
WHERE tipo_despesa = 'ESPECIFICA' 
AND unidade_especifica_id IS NOT NULL;

-- 9. Criar função para validar despesas de condomínio
CREATE OR REPLACE FUNCTION public.validate_despesa_condominio()
RETURNS TRIGGER AS $$
BEGIN
    -- Se é despesa específica, deve ter unidade_especifica_id
    IF NEW.tipo_despesa = 'ESPECIFICA' AND NEW.unidade_especifica_id IS NULL THEN
        RAISE EXCEPTION 'Despesa específica deve ter unidade_especifica_id definido';
    END IF;
    
    -- Se é despesa comum, não deve ter unidade_especifica_id
    IF NEW.tipo_despesa = 'COMUM' AND NEW.unidade_especifica_id IS NOT NULL THEN
        RAISE EXCEPTION 'Despesa comum não deve ter unidade_especifica_id';
    END IF;
    
    -- Se é despesa específica, obra_id deve ser o master da unidade
    IF NEW.tipo_despesa = 'ESPECIFICA' AND NEW.unidade_especifica_id IS NOT NULL THEN
        DECLARE
            master_id uuid;
        BEGIN
            SELECT parent_obra_id INTO master_id 
            FROM public.obras 
            WHERE id = NEW.unidade_especifica_id;
            
            IF master_id IS NOT NULL AND NEW.obra_id != master_id THEN
                RAISE EXCEPTION 'Para despesa específica, obra_id deve ser o condomínio master';
            END IF;
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 10. Criar trigger para validação
DROP TRIGGER IF EXISTS trigger_validate_despesa_condominio ON public.despesas;
CREATE TRIGGER trigger_validate_despesa_condominio
    BEFORE INSERT OR UPDATE ON public.despesas
    FOR EACH ROW
    EXECUTE FUNCTION public.validate_despesa_condominio();

-- 11. Atualizar função de tenant_id para despesas (se necessário)
CREATE OR REPLACE FUNCTION public.set_tenant_id_despesas()
RETURNS TRIGGER AS $$
BEGIN
    -- Manter a lógica existente de tenant_id
    IF NEW.tenant_id IS NULL THEN
        NEW.tenant_id := (
            SELECT tenant_id 
            FROM public.obras 
            WHERE id = NEW.obra_id
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12. Garantir que o trigger de tenant_id existe
DROP TRIGGER IF EXISTS trigger_set_tenant_id_despesas ON public.despesas;
CREATE TRIGGER trigger_set_tenant_id_despesas
    BEFORE INSERT ON public.despesas
    FOR EACH ROW
    EXECUTE FUNCTION public.set_tenant_id_despesas();
