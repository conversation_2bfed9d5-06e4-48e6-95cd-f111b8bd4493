import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';

// ====================================
// 🎯 TIPOS E INTERFACES
// ====================================

export interface DadosObra {
  tipo_obra?: string;
  padrao_obra?: string;
  area_total?: number;
  area_construida?: number;
  estado?: string;
  cidade?: string;
  tipo_condominio?: "VERTICAL" | "HORIZONTAL";
  numero_blocos?: number;
  andares_por_bloco?: number;
  unidades_por_andar?: number;
  numero_unidades?: number;
}

export interface RiskAlert {
  hasRisk: boolean;
  percentageDifference: number;
  suggestedMinimum: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  detailedMessage: string;
  isLoading: boolean;
  error?: string;
  estimatedCost: number;
  userBudget: number;
}

export interface BudgetRiskCalculation {
  estimated_cost: number;
  cost_per_m2: number;
  calculation_params: {
    tipo_obra: string;
    padrao_obra: string;
    area_calculada: number;
    multiplicador_regional: number;
    fator_complexidade?: number;
  };
}

// ====================================
// 🎯 HOOK PRINCIPAL
// ====================================

/**
 * Hook para calcular e alertar sobre riscos orçamentários
 * Compara o orçamento investido com a estimativa paramétrica da IA
 */
export const useOrcamentoRiskAlert = (
  orcamento: number,
  dadosObra: DadosObra
): RiskAlert => {
  
  // Verificar se temos dados suficientes para calcular
  const canCalculate = useMemo(() => {
    return !!(
      dadosObra.area_total && 
      dadosObra.area_total > 0 &&
      dadosObra.tipo_obra &&
      dadosObra.padrao_obra &&
      orcamento > 0
    );
  }, [dadosObra, orcamento]);

  // Query para calcular estimativa rápida via Edge Function
  const { data: calculation, isLoading, error } = useQuery({
    queryKey: ['budget-risk-calculation', dadosObra, orcamento],
    queryFn: async (): Promise<BudgetRiskCalculation> => {
      const response = await fetch('/functions/v1/validate-budget-risk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          dados_obra: dadosObra,
          orcamento_usuario: orcamento
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao calcular estimativa de risco');
      }

      const result = await response.json();
      return result.data;
    },
    enabled: canCalculate,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
  });

  // Cálculo local simplificado como fallback
  const fallbackCalculation = useMemo((): BudgetRiskCalculation | null => {
    if (!canCalculate) return null;

    // Estimativa simplificada baseada no código da Edge Function
    const areaCalculada = dadosObra.area_construida || dadosObra.area_total || 100;
    
    let custoM2Base = 1800; // NORMAL
    switch (dadosObra.padrao_obra) {
      case "ALTO": custoM2Base = 2500; break;
      case "BAIXO": custoM2Base = 1200; break;
      case "LUXO": custoM2Base = 3500; break;
      case "POPULAR": custoM2Base = 1200; break;
    }

    // Multiplicador regional simplificado
    const multiplicadorRegional = 
      dadosObra.estado === "SP" ? 1.2 :
      dadosObra.estado === "RJ" ? 1.15 :
      dadosObra.estado === "GO" ? 0.9 :
      dadosObra.estado === "MG" ? 0.95 : 1.0;

    // Fator de complexidade para condomínios
    let fatorComplexidade = 1.0;
    if (dadosObra.tipo_obra === "R4_MULTIFAMILIAR") {
      fatorComplexidade = dadosObra.tipo_condominio === "VERTICAL" ? 1.45 : 1.25;
    }

    const custoEstimado = areaCalculada * custoM2Base * multiplicadorRegional * fatorComplexidade;

    return {
      estimated_cost: custoEstimado,
      cost_per_m2: custoM2Base * multiplicadorRegional * fatorComplexidade,
      calculation_params: {
        tipo_obra: dadosObra.tipo_obra || "R1_UNIFAMILIAR",
        padrao_obra: dadosObra.padrao_obra || "NORMAL",
        area_calculada: areaCalculada,
        multiplicador_regional: multiplicadorRegional,
        fator_complexidade: fatorComplexidade
      }
    };
  }, [dadosObra, canCalculate]);

  // Usar dados da API ou fallback local
  const finalCalculation = calculation || fallbackCalculation;

  // Análise de risco baseada na comparação
  const riskAnalysis = useMemo((): RiskAlert => {
    if (!canCalculate) {
      return {
        hasRisk: false,
        percentageDifference: 0,
        suggestedMinimum: 0,
        riskLevel: 'low',
        message: '',
        detailedMessage: '',
        isLoading: false,
        estimatedCost: 0,
        userBudget: orcamento
      };
    }

    if (isLoading) {
      return {
        hasRisk: false,
        percentageDifference: 0,
        suggestedMinimum: 0,
        riskLevel: 'low',
        message: 'Calculando risco...',
        detailedMessage: 'Aguarde enquanto calculamos a estimativa de custos.',
        isLoading: true,
        estimatedCost: 0,
        userBudget: orcamento
      };
    }

    if (error || !finalCalculation) {
      return {
        hasRisk: false,
        percentageDifference: 0,
        suggestedMinimum: 0,
        riskLevel: 'low',
        message: 'Não foi possível calcular o risco',
        detailedMessage: 'Erro no cálculo da estimativa. Prossiga com cautela.',
        isLoading: false,
        error: error?.message || 'Erro desconhecido',
        estimatedCost: 0,
        userBudget: orcamento
      };
    }

    const estimatedCost = finalCalculation.estimated_cost;
    const difference = estimatedCost - orcamento;
    const percentageDifference = orcamento > 0 ? (difference / orcamento) * 100 : 0;
    
    // Definir nível de risco
    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
    let hasRisk = false;
    let message = '';
    let detailedMessage = '';

    if (percentageDifference <= 20) {
      // Risco baixo - orçamento adequado
      riskLevel = 'low';
      hasRisk = false;
      message = '✅ Orçamento alinhado com estimativa de mercado';
      detailedMessage = `Seu orçamento está dentro da faixa esperada. Diferença: ${percentageDifference.toFixed(1)}%`;
    } else if (percentageDifference <= 50) {
      // Risco médio
      riskLevel = 'medium';
      hasRisk = true;
      message = `⚠️ Orçamento ${percentageDifference.toFixed(0)}% abaixo da estimativa. Considere revisar.`;
      detailedMessage = `A estimativa de mercado sugere um custo maior. Recomendamos revisar o orçamento para evitar surpresas.`;
    } else if (percentageDifference <= 100) {
      // Risco alto
      riskLevel = 'high';
      hasRisk = true;
      message = `🚨 Orçamento ${percentageDifference.toFixed(0)}% abaixo da estimativa. Risco alto de estouro.`;
      detailedMessage = `Há uma grande discrepância entre seu orçamento e nossa estimativa baseada em dados de mercado. Considere um orçamento mais realista.`;
    } else {
      // Risco crítico
      riskLevel = 'critical';
      hasRisk = true;
      message = `🔴 CRÍTICO: Orçamento ${percentageDifference.toFixed(0)}% abaixo da estimativa. Provável inviabilidade.`;
      detailedMessage = `Seu orçamento está muito abaixo do esperado para este tipo de obra. É altamente recomendável revisar os valores antes de prosseguir.`;
    }

    // Sugestão de valor mínimo (90% da estimativa para dar margem)
    const suggestedMinimum = estimatedCost * 0.9;

    return {
      hasRisk,
      percentageDifference,
      suggestedMinimum,
      riskLevel,
      message,
      detailedMessage,
      isLoading: false,
      estimatedCost,
      userBudget: orcamento
    };
  }, [canCalculate, isLoading, error, finalCalculation, orcamento]);

  return riskAnalysis;
};

/**
 * Hook simplificado para verificação rápida sem dependências externas
 */
export const useQuickBudgetCheck = (
  orcamento: number,
  area: number,
  tipoObra: string = "R1_UNIFAMILIAR",
  padraoObra: string = "NORMAL",
  estado: string = "GO"
): { isLikelyUnderbudget: boolean; estimatedCost: number } => {
  
  return useMemo(() => {
    if (!orcamento || !area || orcamento <= 0 || area <= 0) {
      return { isLikelyUnderbudget: false, estimatedCost: 0 };
    }

    // Cálculo super simplificado
    let custoM2 = 1800; // Base
    switch (padraoObra) {
      case "ALTO": custoM2 = 2500; break;
      case "BAIXO": custoM2 = 1200; break;
      case "LUXO": custoM2 = 3500; break;
      case "POPULAR": custoM2 = 1200; break;
    }

    const multiplicador = estado === "SP" ? 1.2 : estado === "RJ" ? 1.15 : estado === "GO" ? 0.9 : 1.0;
    const complexidade = tipoObra === "R4_MULTIFAMILIAR" ? 1.3 : 1.0;
    
    const estimatedCost = area * custoM2 * multiplicador * complexidade;
    const isLikelyUnderbudget = orcamento < (estimatedCost * 0.7); // 30% de margem

    return { isLikelyUnderbudget, estimatedCost };
  }, [orcamento, area, tipoObra, padraoObra, estado]);
};