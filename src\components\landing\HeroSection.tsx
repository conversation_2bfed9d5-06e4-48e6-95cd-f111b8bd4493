import { motion } from "framer-motion";
import { ArrowRight, Building2 } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

import background3 from "@/assets/images/background3.jpg";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/auth/hooks";

import LeadChatbot from "./LeadChatbot";
import { VideoModal } from "./VideoModal";

export const HeroSection = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [videoModalOpen, setVideoModalOpen] = useState(false);
  const [leadChatbotOpen, setLeadChatbotOpen] = useState(false);

  const handleGetStarted = () => {
    setIsLoading(true);
    if (user) {
      navigate("/dashboard");
    } else {
      setLeadChatbotOpen(true);
    }
    setIsLoading(false);
  };

  return (
    <section className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Imagem de fundo com overlay, igual ao Login */}
      <img
        src={background3}
        alt="Background ObraVision"
        className="absolute inset-0 w-full h-full object-cover z-0"
        style={{ filter: "blur(0px) brightness(0.6)" }}
      />
      <div className="absolute inset-0 bg-black/70 z-10" />
      <div className="container mx-auto px-4 relative z-20 flex flex-col justify-center min-h-[80vh]">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-7xl mx-auto"
        >
          {/* Badge profissional */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-6"
          >
            <Badge className="bg-badge-implemented-bg text-badge-implemented-text border border-slate-200 dark:border-slate-700 px-4 py-2 font-medium">
              <Building2 className="w-4 h-4 mr-2" />
              Sistema de Gestão de Obras
            </Badge>
          </motion.div>

          {/* Título principal - mais direto */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-center md:text-left text-white"
          >
            Gestão de obras com{" "}
            <span className="bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
              IA especializada
            </span>
          </motion.h1>

          {/* Subtítulo mais focado em resultados */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-xl md:text-2xl text-white mb-8 leading-relaxed text-center md:text-left"
          >
            Plataforma completa para construtoras: controle financeiro,
            contratos inteligentes, orçamentos precisos e insights de IA.
          </motion.p>

          {/* CTA principal */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-center md:justify-start"
          >
            <Button
              variant="default"
              size="lg"
              onClick={handleGetStarted}
              disabled={isLoading}
              className="bg-cta-primary hover:bg-cta-hover text-cta-text font-bold group transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-lg px-8 py-6 border-0 focus:outline-none focus:ring-2 focus:ring-cta-primary focus:ring-offset-2"
            >
              <span className="font-semibold">
                {isLoading
                  ? "Carregando..."
                  : user
                  ? "Ir para Dashboard"
                  : "Conversar com IA"}
              </span>
              {!isLoading && (
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              )}
            </Button>
          </motion.div>

          {/* Descrição do chatbot */}
          {!user && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mt-4 text-center md:text-left"
            >
              <p className="text-sm text-white/90">
                💬 <strong>Nossa IA especializada</strong> vai capturar suas
                informações e responder suas dúvidas sobre gestão de obras
              </p>
            </motion.div>
          )}
        </motion.div>
      </div>
      <VideoModal
        isOpen={videoModalOpen}
        onClose={() => setVideoModalOpen(false)}
      />
      <LeadChatbot
        isOpen={leadChatbotOpen}
        onClose={() => setLeadChatbotOpen(false)}
      />
    </section>
  );
};
