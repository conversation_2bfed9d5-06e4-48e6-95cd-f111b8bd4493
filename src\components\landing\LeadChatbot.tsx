import { motion } from 'framer-motion';
import { CheckCircle,Loader2, Send, User } from 'lucide-react';
import React, { useEffect,useRef, useState } from 'react';

import logoImageLight from '@/assets/logo/logo_image_light.png';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription,DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { useAnalytics } from '@/services/analyticsApi';

interface Message {
  id: string;
  content: string;
  isBot: boolean;
  timestamp: Date;
}

interface LeadData {
  nome?: string;
  email?: string;
  telefone?: string;
  empresa?: string;
  cargo?: string;
  interesse?: string;
}

interface LeadChatbotProps {
  isOpen: boolean;
  onClose: () => void;
}

const LeadChatbot: React.FC<LeadChatbotProps> = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: '👋 Olá! Eu sou a IA do ObraVision. Vou te ajudar a conhecer nossa plataforma! Qual é o seu nome?',
      isBot: true,
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [leadData, setLeadData] = useState<LeadData>({});
  const [currentStep, setCurrentStep] = useState('nome');
  const [isLeadCaptured, setIsLeadCaptured] = useState(false);
  const [typingMessage, setTypingMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { trackEvent, trackLead, trackAIUsage } = useAnalytics();

  // 📊 Track abertura do chatbot
  useEffect(() => {
    if (isOpen) {
      // Track sem falhar o fluxo se der erro
      trackEvent({
        event_type: 'chatbot_opened',
        page: 'landing_page',
        properties: {
          timestamp: new Date().toISOString(),
          source: 'landing_page_cta'
        }
      }).catch(() => {
        // Silencioso - não impactar UX
      });
    }
  }, [isOpen, trackEvent]);

  // Log apenas em desenvolvimento e quando há mudanças significativas
  const isDev = import.meta.env.DEV;
  if (isDev && (currentStep !== 'nome' || Object.keys(leadData).length > 0)) {
    console.log('🚀 Estado do chatbot:', {
      currentStep,
      isLeadCaptured,
      leadDataKeys: Object.keys(leadData),
      messagesCount: messages.length
    });
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Função para simular efeito de digitação
  const typeMessage = async (message: string, delay = 50) => {
    return new Promise<void>((resolve) => {
      setIsTyping(true);
      setTypingMessage('');
      
      let currentIndex = 0;
      const timer = setInterval(() => {
        if (currentIndex <= message.length) {
          setTypingMessage(message.slice(0, currentIndex));
          currentIndex++;
          
          // Auto-scroll durante a digitação
          setTimeout(() => scrollToBottom(), 50);
        } else {
          clearInterval(timer);
          setIsTyping(false);
          setTypingMessage('');
          
          // Adicionar mensagem completa às mensagens
          const botMessage: Message = {
            id: Date.now().toString(),
            content: message,
            isBot: true,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, botMessage]);
          resolve();
        }
      }, delay);
    });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Fluxo de captura de leads - CORRIGIDO: cada step tem sua própria pergunta
  const leadFlow = {
    nome: {
      nextStep: 'email',
      question: '', // pergunta inicial já foi feita
      validation: (value: string) => value.length >= 2
    },
    email: {
      nextStep: 'telefone', 
      question: 'Perfeito! Agora me diga qual é o seu email para contato?',
      validation: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
    },
    telefone: {
      nextStep: 'empresa',
      question: 'Ótimo! Qual é o seu telefone para contato?',
      validation: (value: string) => value.length >= 10 // telefone obrigatório
    },
    empresa: {
      nextStep: 'cargo',
      question: 'Qual é o nome da sua empresa ou você é profissional autônomo?',
      validation: (value: string) => value.length >= 2
    },
    cargo: {
      nextStep: 'interesse',
      question: 'Qual é o seu cargo/função?',
      validation: (value: string) => value.length >= 2
    },
    interesse: {
      nextStep: 'completed',
      question: 'Por último, qual é o seu principal interesse no ObraVision? (ex: controlar custos, organizar obras, etc.)',
      validation: (value: string) => value.length >= 2
    }
  };

  const saveLeadToDatabase = async (leadData: LeadData) => {
    try {
      if (import.meta.env.DEV) {
        console.log('Salvando lead no Supabase...');
      }
      
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(`${supabaseUrl}/functions/v1/lead-capture`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          lead: {
            email: leadData.email,
            nome: leadData.nome,
            telefone: leadData.telefone, // Agora sempre preenchido
            empresa: leadData.empresa,
            cargo: leadData.cargo,
            interesse_nivel: 'alto', // Lead vem do chatbot, presumir alto interesse
            origem: 'chatbot_landing_page',
            principal_desafio: leadData.interesse
          },
          context: {
            page_url: window.location.href,
            referrer: document.referrer,
            user_agent: navigator.userAgent
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (import.meta.env.DEV) {
          console.log('✅ Lead salvo com sucesso:', result);
        }
        
        // Enviar notificação para a equipe
        await sendNotificationToTeam(leadData, result);
        
        return true;
      } else {
        const error = await response.json().catch(() => ({}));
        console.error('❌ Erro ao salvar lead:', error);
        return false;
      }
    } catch (error) {
      console.error('❌ Erro ao salvar lead no Supabase:', error);
      return false;
    }
  };

  const sendNotificationToTeam = async (leadData: LeadData, result: unknown) => {
    try {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      await fetch(`${supabaseUrl}/functions/v1/notification-handler`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          tipo_notificacao: 'novo_lead_capturado',
          destinatario: '<EMAIL>', // ou buscar da configuração
          canal: 'email',
          dados: {
            nome: leadData.nome,
            email: leadData.email,
            empresa: leadData.empresa,
            cargo: leadData.cargo,
            interesse: leadData.interesse,
            origem: 'chatbot_landing_page',
            score: result.score || 0,
            prioridade: result.prioridade || 'normal',
            data_captura: new Date().toLocaleString('pt-BR')
          }
        })
      });
    } catch (error) {
      console.error('❌ Erro ao enviar notificação:', error);
      // Não falhar o fluxo por causa da notificação
    }
  };

  const sendAIMessage = async (userMessage: string) => {
    try {
      // Usar Edge Function do Supabase para segurança da API key
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(`${supabaseUrl}/functions/v1/ai-landing-chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          message: userMessage,
          visitor_id: `visitor_${Date.now()}`
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (response.status === 429) {
          return 'Você atingiu o limite de mensagens. Aguarde alguns minutos para continuar conversando! 😊';
        }
        throw new Error(errorData.error || 'Erro na API da IA');
      }

      const data = await response.json();
      return data.response || 'Desculpe, não consegui processar sua pergunta.';
    } catch (error) {
      console.error('Erro na IA:', error);
      return 'Desculpe, estou com dificuldades técnicas no momento. Mas posso te dizer que o ObraVision oferece gestão completa de obras, orçamento inteligente com IA, sistema SINAPI integrado e muito mais!';
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    // Log apenas em desenvolvimento para debug
    if (import.meta.env.DEV) {
      console.log('📥 handleSendMessage:', inputValue.trim());
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      isBot: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue.trim();
    setInputValue('');
    setIsLoading(true);

    try {
      if (!isLeadCaptured) {
        // Processo de captura de leads
        const currentStepData = leadFlow[currentStep as keyof typeof leadFlow];
        
        if (import.meta.env.DEV) {
          console.log(`Step: ${currentStep} -> ${currentInput}`);
        }
        
        if (currentStepData && currentStepData.validation && !currentStepData.validation(currentInput)) {
          if (import.meta.env.DEV) {
            console.log('❌ Validação falhou:', currentInput);
          }
          let errorMessage = 'Por favor, verifique a informação digitada.';
          if (currentStep === 'email') {
            errorMessage = 'Por favor, digite um email válido.';
          } else if (currentStep === 'nome') {
            errorMessage = 'Por favor, digite seu nome completo.';
          } else if (currentStep === 'telefone') {
            errorMessage = 'Por favor, digite um telefone válido (mínimo 10 dígitos).';
          }
          
          const botMessage: Message = {
            id: (Date.now() + 1).toString(),
            content: errorMessage,
            isBot: true,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, botMessage]);
          setIsLoading(false);
          return;
        }

        // Atualizar dados do lead
        const updatedLeadData = { ...leadData, [currentStep]: currentInput };
        setLeadData(updatedLeadData);
        
        if (import.meta.env.DEV) {
          console.log('✅ Lead atualizado:', Object.keys(updatedLeadData));
        }

        // VERIFICAÇÃO EXPLÍCITA SE É O ÚLTIMO STEP
        if (currentStep === 'interesse') {
          if (import.meta.env.DEV) {
            console.log('🎯 Último step - enviando webhook...');
          }
          
          // Finalizar captura de leads
          const saveSuccess = await saveLeadToDatabase(updatedLeadData);
          
          // 📊 Track lead capturado
          if (saveSuccess) {
            // Track sem bloquear o fluxo
            trackLead({
              email: updatedLeadData.email!,
              source: 'chatbot_landing_page',
              campaign: 'lead_capture_flow',
              referrer: document.referrer
            }).catch(() => {});
            
            // Track conversão de lead
            trackEvent({
              event_type: 'conversion_lead',
              page: 'landing_page',
              properties: {
                lead_source: 'chatbot',
                lead_email: updatedLeadData.email,
                lead_empresa: updatedLeadData.empresa,
                lead_cargo: updatedLeadData.cargo,
                save_success: saveSuccess
              }
            }).catch(() => {});
          }
          
          const completionMessage = saveSuccess 
            ? `🎉 Perfeito! Suas informações foram salvas com sucesso.\n\nNossa equipe comercial foi notificada e entrará em contato em breve para apresentar como o ObraVision pode transformar sua gestão de obras!\n\nEnquanto isso, posso responder suas dúvidas sobre nossa plataforma. O que você gostaria de saber?`
            : `✅ Obrigado pelas informações! Agora posso responder suas dúvidas sobre o ObraVision. O que você gostaria de saber?`;

          // Usar efeito de digitação para a mensagem final
          await typeMessage(completionMessage, 30); // 30ms delay entre caracteres
          
          setIsLeadCaptured(true);
          
          if (saveSuccess) {
            toast({
              title: "Dados Salvos!",
              description: "Informações salvas e equipe notificada!",
              variant: "default",
            });
          }
        } else {
          // Próxima pergunta do fluxo
          const nextStep = currentStepData?.nextStep || 'completed';
          
          if (import.meta.env.DEV) {
            console.log(`${currentStep} -> ${nextStep}`);
          }
          
          setCurrentStep(nextStep);
          
          const nextQuestion = leadFlow[nextStep as keyof typeof leadFlow]?.question;
          
          if (nextQuestion) {
            const botMessage: Message = {
              id: (Date.now() + 1).toString(),
              content: nextQuestion,
              isBot: true,
              timestamp: new Date()
            };
            
            setMessages(prev => [...prev, botMessage]);
          }
        }
      } else {
        if (import.meta.env.DEV) {
          console.log('💬 Conversa com IA');
        }
        // Conversa com IA após captura de leads - USAR PRD CONTEXT
        const aiResponse = await sendAIMessage(currentInput);
        
        // 📊 Track uso da IA
        trackAIUsage('chat', {
          user_message: currentInput,
          ai_response_length: aiResponse.length,
          conversation_stage: 'post_lead_capture',
          source: 'chatbot_landing_page'
        }).catch(() => {});
        
        const botMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: aiResponse,
          isBot: true,
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, botMessage]);
      }
    } catch (error) {
      console.error('💥 ERRO em handleSendMessage:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Desculpe, ocorreu um erro. Mas posso te contar que o ObraVision é a plataforma mais completa para gestão de obras do Brasil!',
        isBot: true,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      // Log removido para console limpo
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const _resetChat = () => {
    setMessages([{
      id: '1',
      content: '👋 Olá! Eu sou a IA do ObraVision. Vou te ajudar a conhecer nossa plataforma! Qual é o seu nome?',
      isBot: true,
      timestamp: new Date()
    }]);
    setLeadData({});
    setCurrentStep('nome');
    setIsLeadCaptured(false);
    setInputValue('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg max-h-[85vh] p-0 border-0 bg-transparent shadow-none">
        {/* Elementos de acessibilidade ocultos visualmente */}
        <DialogTitle className="sr-only">
          Chat com Assistente IA do ObraVision
        </DialogTitle>
        <DialogDescription className="sr-only">
          Converse com nossa IA especializada em construção civil para esclarecer dúvidas sobre o ObraVision e suas funcionalidades
        </DialogDescription>
        
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="h-[600px] flex flex-col bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-700/50 backdrop-blur-xl overflow-hidden"
        >
          {/* Header moderno com gradiente */}
          <div className="relative bg-gradient-to-r from-orange-500 via-yellow-500 to-orange-600 p-4">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center p-2 shadow-lg">
                  <img 
                    src={logoImageLight} 
                    alt="ObraVision" 
                    className="h-8 w-8 object-contain"
                  />
                </div>
                <div>
                  <h3 className="font-bold text-lg text-white drop-shadow-sm">IA do ObraVision</h3>
                  <p className="text-sm text-white/90 font-medium">
                    {!isLeadCaptured ? 'Especialista em Construção Civil' : 'Pronto para suas perguntas!'}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {isLeadCaptured && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="bg-green-500/20 backdrop-blur-sm rounded-full p-2"
                  >
                    <CheckCircle className="h-5 w-5 text-white" />
                  </motion.div>
                )}
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg"></div>
              </div>
            </div>
          </div>

          {/* Messages Area - Moderno */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-slate-800/50 to-slate-900/80 backdrop-blur-sm">
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 15, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.4, type: "spring", bounce: 0.3 }}
                className={`flex gap-3 ${message.isBot ? 'justify-start' : 'justify-end'}`}
              >
                {message.isBot && (
                  <div className="flex-shrink-0 mt-1">
                    <div className="h-8 w-8 rounded-xl bg-gradient-to-br from-orange-500 to-yellow-500 flex items-center justify-center p-1.5 shadow-lg border border-white/20">
                      <img 
                        src={logoImageLight} 
                        alt="Bot" 
                        className="h-5 w-5 object-contain"
                      />
                    </div>
                  </div>
                )}
                
                <div className={`max-w-[80%] group ${
                  message.isBot ? 'order-2' : 'order-1'
                }`}>
                  <div className={`rounded-2xl p-4 shadow-lg backdrop-blur-sm border ${
                    message.isBot 
                      ? 'bg-gradient-to-br from-slate-700/90 to-slate-800/90 text-white border-slate-600/50 rounded-tl-md' 
                      : 'bg-gradient-to-br from-orange-500 to-yellow-500 text-white border-orange-400/50 rounded-tr-md'
                  }`}>
                    <div className="whitespace-pre-wrap text-sm leading-relaxed font-medium">
                      {message.content}
                    </div>
                    <div className={`text-xs mt-2 ${
                      message.isBot ? 'text-slate-400' : 'text-white/80'
                    }`}>
                      {message.timestamp.toLocaleTimeString('pt-BR', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                </div>

                {!message.isBot && (
                  <div className="flex-shrink-0 mt-1 order-3">
                    <div className="h-8 w-8 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg border border-white/20">
                      <User className="h-4 w-4 text-white" />
                    </div>
                  </div>
                )}
              </motion.div>
            ))}

            {isLoading && (
              <motion.div
                initial={{ opacity: 0, y: 15, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="flex gap-3 justify-start"
              >
                <div className="flex-shrink-0 mt-1">
                  <div className="h-8 w-8 rounded-xl bg-gradient-to-br from-orange-500 to-yellow-500 flex items-center justify-center p-1.5 shadow-lg border border-white/20">
                    <img 
                      src={logoImageLight} 
                      alt="Bot" 
                      className="h-5 w-5 object-contain"
                    />
                  </div>
                </div>
                <div className="bg-gradient-to-br from-slate-700/90 to-slate-800/90 backdrop-blur-sm border border-slate-600/50 rounded-2xl rounded-tl-md p-4 shadow-lg">
                  <div className="flex items-center gap-3">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-sm text-slate-300 font-medium">
                      {!isLeadCaptured ? 'Processando...' : 'Pensando...'}
                    </span>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Efeito de digitação moderno */}
            {isTyping && typingMessage && (
              <motion.div
                initial={{ opacity: 0, y: 15, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="flex gap-3 justify-start"
              >
                <div className="flex-shrink-0 mt-1">
                  <div className="h-8 w-8 rounded-xl bg-gradient-to-br from-orange-500 to-yellow-500 flex items-center justify-center p-1.5 shadow-lg border border-white/20">
                    <img 
                      src={logoImageLight} 
                      alt="Bot" 
                      className="h-5 w-5 object-contain"
                    />
                  </div>
                </div>
                <div className="bg-gradient-to-br from-slate-700/90 to-slate-800/90 backdrop-blur-sm border border-slate-600/50 rounded-2xl rounded-tl-md p-4 shadow-lg max-w-[80%]">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed font-medium text-white">
                    {typingMessage}
                    <span className="inline-block w-0.5 h-4 bg-orange-400 ml-1 animate-pulse"></span>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Ações rápidas minimalistas */}
            {isLeadCaptured && (
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mt-4 flex justify-center gap-3"
              >
                {[
                  { icon: "💰", text: "Preços", query: "Quanto custa o ObraVision?" },
                  { icon: "🔧", text: "Como funciona", query: "Como funciona o ObraVision?" },
                  { icon: "📞", text: "Contato", query: "Como posso falar com vocês?" }
                ].map((action, index) => (
                  <motion.button
                    key={index}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      setInputValue(action.query);
                      setTimeout(handleSendMessage, 100);
                    }}
                    className="flex flex-col items-center gap-1 p-3 bg-slate-700/30 hover:bg-slate-600/40 rounded-xl transition-all duration-200 border border-slate-600/20 hover:border-orange-500/30 min-w-[70px]"
                  >
                    <span className="text-lg">{action.icon}</span>
                    <span className="text-xs text-slate-300 font-medium">{action.text}</span>
                  </motion.button>
                ))}
              </motion.div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input Area - Moderno */}
          <div className="p-4 bg-gradient-to-r from-slate-800/90 to-slate-900/90 backdrop-blur-sm border-t border-slate-600/50">
            <div className="flex gap-3 items-end">
              <div className="flex-1 relative">
                <div className="relative">
                  <Input
                    ref={inputRef}
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder={
                      !isLeadCaptured 
                        ? "Digite sua resposta..." 
                        : "Digite sua pergunta..."
                    }
                    disabled={isLoading}
                    className="text-sm h-12 bg-slate-700/80 backdrop-blur-sm text-white placeholder-slate-400 border border-slate-600/50 focus:border-orange-500/70 focus:ring-2 focus:ring-orange-500/20 rounded-xl pr-4 shadow-lg transition-all duration-200"
                  />
                  <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-500/5 to-yellow-500/5 pointer-events-none"></div>
                </div>
              </div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isLoading}
                  className="bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 disabled:from-slate-600 disabled:to-slate-700 shrink-0 h-12 w-12 p-0 rounded-xl shadow-lg border border-orange-400/30 transition-all duration-200"
                >
                  {isLoading ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    <Send className="h-5 w-5" />
                  )}
                </Button>
              </motion.div>
            </div>
            
            <div className="mt-3 text-center">
              <p className="text-xs text-slate-400 font-medium">
                {isLeadCaptured 
                  ? "🤖 Chat com IA • Perguntas sobre ObraVision • Powered by DeepSeek"
                  : "📋 Informações para nossa equipe comercial entrar em contato"
                }
              </p>
            </div>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};

export default LeadChatbot;