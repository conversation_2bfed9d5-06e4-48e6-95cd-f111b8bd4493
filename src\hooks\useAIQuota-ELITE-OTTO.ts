import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

import { useAuth } from '@/contexts/auth/hooks';
import { useSubscription } from '@/hooks/useSubscription';
import { supabase } from '@/integrations/supabase/client';

// Definição dos tipos de funcionalidades de IA
export type AIFeatureType = 'chat' | 'budget' | 'contract' | 'sinapi';

// Quotas por tipo de plano
const AI_QUOTAS = {
  free: {
    chat: 20,      // 20 perguntas/dia
    budget: 1,     // 1 orçamento/dia
    contract: 1,   // 1 análise/dia
    sinapi: 15,    // 15 consultas/dia
  },
  basic: {
    chat: 0,       // Sem IA no plano básico
    budget: 0,
    contract: 0,
    sinapi: 0,
  },
  pro: {
    chat: -1,      // Ilimitado
    budget: -1,
    contract: -1,
    sinapi: -1,
  },
  enterprise: {
    chat: -1,      // Ilimitado
    budget: -1,
    contract: -1,
    sinapi: -1,
  },
  trialing: {
    chat: 5,       // 5 chats/dia para trial
    budget: 1,     // 1 orçamento TOTAL durante todo período trial
    contract: 0,   // Sem contratos no trial
    sinapi: 15,    // 15 consultas/dia
  }
} as const;

interface AIUsageData {
  id?: string;
  user_id: string;
  date: string;
  chat_requests: number;
  budget_requests: number;
  contract_requests: number;
  sinapi_requests: number;
  total_tokens: number;
}

interface QuotaStatus {
  current: number;
  limit: number;
  remaining: number;
  percentage: number;
  canUse: boolean;
}

interface AIQuotaResult {
  usage: AIUsageData | null;
  quotas: {
    chat: QuotaStatus;
    budget: QuotaStatus;
    contract: QuotaStatus;
    sinapi: QuotaStatus;
  };
  checkQuota: (feature: AIFeatureType) => boolean;
  incrementUsage: (feature: AIFeatureType, tokens?: number) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
}

export const useAIQuota = (): AIQuotaResult => {
  const { user } = useAuth();
  const { subscription } = useSubscription();
  const queryClient = useQueryClient();
  const [error, setError] = useState<Error | null>(null);

  // Determinar o plano atual para definir quotas
  const currentPlan = subscription?.status === 'trialing' 
    ? 'trialing' 
    : subscription?.plan_type || 'free';

  const planLimits = AI_QUOTAS[currentPlan as keyof typeof AI_QUOTAS];

  // Query para buscar uso total do trial (se aplicável)
  const { data: trialUsage } = useQuery({
    queryKey: ['ai-trial-usage', user?.id],
    queryFn: async () => {
      if (!user?.id || currentPlan !== 'trialing') return null;

      const { data, error } = await supabase
        .from('ai_trial_usage')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Erro ao buscar uso do trial:', error);
      }

      return data;
    },
    enabled: !!user?.id && currentPlan === 'trialing',
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  // Query para buscar uso atual do dia
  const { data: usage, isLoading } = useQuery({
    queryKey: ['ai-usage', user?.id, new Date().toISOString().split('T')[0]],
    queryFn: async (): Promise<AIUsageData | null> => {
      if (!user?.id) return null;

      const today = new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase
        .from('ai_usage_tracking')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', today)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
        throw new Error(`Erro ao buscar uso de IA: ${error.message}`);
      }

      return data || {
        user_id: user.id,
        date: today,
        chat_requests: 0,
        budget_requests: 0,
        contract_requests: 0,
        sinapi_requests: 0,
        total_tokens: 0,
      };
    },
    enabled: !!user?.id,
    staleTime: 1000 * 60, // 1 minuto
  });

  // Função helper para calcular status de quota
  const calculateQuotaStatus = (current: number, limit: number): QuotaStatus => {
    const isUnlimited = limit === -1;
    const remaining = isUnlimited ? -1 : Math.max(0, limit - current);
    const percentage = isUnlimited ? 0 : Math.min(100, (current / limit) * 100);
    const canUse = isUnlimited || current < limit;

    return {
      current,
      limit,
      remaining,
      percentage,
      canUse,
    };
  };

  // Calcular quotas atuais
  // Para orçamentos no trial, usar o total desde o início do trial, não apenas hoje
  const budgetCurrent = currentPlan === 'trialing' 
    ? (trialUsage?.total_budget_requests || 0)
    : (usage?.budget_requests || 0);

  const quotas = {
    chat: calculateQuotaStatus(usage?.chat_requests || 0, planLimits.chat),
    budget: calculateQuotaStatus(budgetCurrent, planLimits.budget),
    contract: calculateQuotaStatus(usage?.contract_requests || 0, planLimits.contract),
    sinapi: calculateQuotaStatus(usage?.sinapi_requests || 0, planLimits.sinapi),
  };

  // Função para verificar se pode usar uma funcionalidade
  const checkQuota = (feature: AIFeatureType): boolean => {
    return quotas[feature].canUse;
  };

  // Mutation para incrementar uso
  const incrementMutation = useMutation({
    mutationFn: async ({ feature, tokens = 0 }: { feature: AIFeatureType; tokens?: number }) => {
      if (!user?.id) throw new Error('Usuário não autenticado');

      const today = new Date().toISOString().split('T')[0];
      const columnMap = {
        chat: 'chat_requests',
        budget: 'budget_requests', 
        contract: 'contract_requests',
        sinapi: 'sinapi_requests',
      };

      // Se for orçamento em trial, também atualizar ai_trial_usage
      if (feature === 'budget' && currentPlan === 'trialing') {
        const { error: trialError } = await supabase
          .from('ai_trial_usage')
          .update({
            total_budget_requests: (trialUsage?.total_budget_requests || 0) + 1,
          })
          .eq('user_id', user.id);

        if (trialError) {
          console.error('Erro ao atualizar uso do trial:', trialError);
        }
      }

      // Tentar fazer update primeiro (upsert)
      const { data, error } = await supabase
        .from('ai_usage_tracking')
        .upsert({
          user_id: user.id,
          date: today,
          [columnMap[feature]]: (usage?.[columnMap[feature] as keyof AIUsageData] as number || 0) + 1,
          total_tokens: (usage?.total_tokens || 0) + tokens,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Erro ao incrementar uso de IA: ${error.message}`);
      }

      return data;
    },
    onSuccess: () => {
      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({
        queryKey: ['ai-usage', user?.id],
      });
      
      // Se for trial, invalidar também o cache do trial usage
      if (currentPlan === 'trialing') {
        queryClient.invalidateQueries({
          queryKey: ['ai-trial-usage', user?.id],
        });
      }
    },
    onError: (error) => {
      setError(error as Error);
    },
  });

  const incrementUsage = async (feature: AIFeatureType, tokens = 0): Promise<void> => {
    try {
      setError(null);
      
      // Verificar quota antes de incrementar
      if (!checkQuota(feature)) {
        throw new Error(`Quota de ${feature} excedida. Faça upgrade do seu plano.`);
      }

      await incrementMutation.mutateAsync({ feature, tokens });
    } catch (error) {
      setError(error as Error);
      throw error;
    }
  };

  return {
    usage,
    quotas,
    checkQuota,
    incrementUsage,
    isLoading,
    error,
  };
};

// Hook helper para funcionalidades específicas
export const useAIChatQuota = () => {
  const aiQuota = useAIQuota();
  return {
    ...aiQuota.quotas.chat,
    checkQuota: () => aiQuota.checkQuota('chat'),
    incrementUsage: (tokens?: number) => aiQuota.incrementUsage('chat', tokens),
  };
};

export const useAIBudgetQuota = () => {
  const aiQuota = useAIQuota();
  return {
    ...aiQuota.quotas.budget,
    checkQuota: () => aiQuota.checkQuota('budget'),
    incrementUsage: (tokens?: number) => aiQuota.incrementUsage('budget', tokens),
  };
};

export const useAIContractQuota = () => {
  const aiQuota = useAIQuota();
  return {
    ...aiQuota.quotas.contract,
    checkQuota: () => aiQuota.checkQuota('contract'),
    incrementUsage: (tokens?: number) => aiQuota.incrementUsage('contract', tokens),
  };
};

export const useAISinapiQuota = () => {
  const aiQuota = useAIQuota();
  return {
    ...aiQuota.quotas.sinapi,
    checkQuota: () => aiQuota.checkQuota('sinapi'),
    incrementUsage: (tokens?: number) => aiQuota.incrementUsage('sinapi', tokens),
  };
};