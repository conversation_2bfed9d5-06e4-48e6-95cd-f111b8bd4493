{"mcpServers": {"context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": ["resolve-library-id", "get-library-docs"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": ["list_projects", "list_edge_functions", "deploy_edge_function"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequentialthinking"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "*****************************************/prefeituravirtual"], "disabled": false, "autoApprove": ["query"]}, "mcpServers": {"Playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}}}}