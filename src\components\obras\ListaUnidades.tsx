import { useQueryClient } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Eye, Trash2 } from "lucide-react";
import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";

import { CondominioConfirmationDialog } from "@/components/obras/CondominioConfirmationDialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable } from "@/components/ui/data-table";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/auth/hooks";
import type { Obra } from "@/types/api";

interface ListaUnidadesProps {
  condomínioId: string;
  unidades: Obra[];
  onUnidadeSelect?: (unidadeId: string) => void;
  onBatchAction?: (action: string, selectedIds: string[]) => void;
  isLoading?: boolean;
  pageSize?: number;
  enablePagination?: boolean;
}

export const ListaUnidades = ({
  condomínioId,
  unidades,
  onUnidadeSelect,
  onBatchAction,
  isLoading = false,
  pageSize = 50,
  enablePagination = true,
}: ListaUnidadesProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const [selectedUnidades, setSelectedUnidades] = useState<string[]>([]);
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    type: "deleteUnit";
    unitId?: string;
    unitName?: string;
    condominioName?: string;
  }>({
    isOpen: false,
    type: "deleteUnit",
  });

  // Função para navegar para detalhes da unidade
  const handleNavigateToUnidade = (unidadeId: string) => {
    if (onUnidadeSelect) {
      onUnidadeSelect(unidadeId);
    } else {
      navigate(`/dashboard/obras/${unidadeId}`);
    }
  };

  // Função para marcar unidade como concluída
  const handleMarkAsCompleted = async (unidadeId: string, unidadeNome: string) => {
    try {
      console.log('Funcionalidade temporariamente desabilitada - cache do useObras');
      
      toast({
        title: "Aviso",
        description: "Funcionalidade temporariamente desabilitada devido a problemas de cache",
      });
    } catch (error) {
      console.error("Erro ao marcar unidade como concluída:", error);
      toast({
        title: "Erro",
        description: "Não foi possível marcar a unidade como concluída.",
        variant: "destructive",
      });
    }
  };

  // Colunas da tabela
  const columns: ColumnDef<Obra>[] = useMemo(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Selecionar todas"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Selecionar linha"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "nome",
        header: "Nome da Unidade",
        cell: ({ row }) => {
          const obra = row.original;
          return (
            <div className="flex flex-col">
              <span className="font-medium">{obra.nome}</span>
              {obra.descricao && (
                <span className="text-sm text-muted-foreground">
                  {obra.descricao}
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => {
          const status = row.getValue("status") as string;
          return (
            <Badge variant={status === "concluida" ? "default" : "secondary"}>
              {status === "concluida" ? "Concluída" : "Em Andamento"}
            </Badge>
          );
        },
      },
      {
        accessorKey: "progresso",
        header: "Progresso",
        cell: ({ row }) => {
          const progresso = row.getValue("progresso") as number;
          return (
            <div className="flex items-center space-x-2">
              <div className="w-full bg-secondary rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: `${progresso}%` }}
                />
              </div>
              <span className="text-sm font-medium">{progresso}%</span>
            </div>
          );
        },
      },
      {
        id: "actions",
        header: "Ações",
        cell: ({ row }) => {
          const obra = row.original;
          return (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigateToUnidade(obra.id)}
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/dashboard/obras/${obra.id}/edit`)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              {obra.progresso < 100 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleMarkAsCompleted(obra.id, obra.nome)}
                >
                  ✓ Concluir
                </Button>
              )}
            </div>
          );
        },
      },
    ],
    [navigate]
  );

  return (
    <div className="space-y-4">
      <DataTable
        columns={columns}
        data={unidades}
        loading={isLoading}
        pagination={enablePagination}
        pageSize={pageSize}
      />

      <CondominioConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        type={confirmationDialog.type}
        unitId={confirmationDialog.unitId}
        unitName={confirmationDialog.unitName}
        condominioName={confirmationDialog.condominioName}
        onConfirm={async () => {
          console.log('Funcionalidade temporariamente desabilitada');
        }}
        onCancel={() =>
          setConfirmationDialog(prev => ({ ...prev, isOpen: false }))
        }
      />
    </div>
  );
};