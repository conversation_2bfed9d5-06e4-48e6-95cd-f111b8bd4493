#!/bin/bash

# Deploy da edge function ai-chat-handler-v2 com quotas atualizadas

echo "Fazendo deploy da edge function ai-chat-handler-v2..."

# Fazer deploy usando npx supabase
npx supabase functions deploy ai-chat-handler-v2 --no-verify-jwt

echo "Deploy concluído!"
echo ""
echo "IMPORTANTE: Execute também estes comandos SQL no Supabase Dashboard:"
echo ""
echo "-- 1. <PERSON><PERSON>r registro ai_trial_usage se não existir"
echo "INSERT INTO ai_trial_usage ("
echo "  id,"
echo "  user_id,"
echo "  subscription_id,"
echo "  trial_start_date,"
echo "  trial_end_date,"
echo "  total_budget_requests,"
echo "  created_at,"
echo "  updated_at"
echo ") VALUES ("
echo "  gen_random_uuid(),"
echo "  'eea9b847-2e26-42a9-b620-43cff6494472',"
echo "  '263d10cd-0827-4052-bdad-e03607182210',"
echo "  '2025-08-01 19:55:40.968061+00',"
echo "  '2025-08-08 19:55:40.968061+00',"
echo "  0,"
echo "  NOW(),"
echo "  NOW()"
echo ") ON CONFLICT (user_id) DO NOTHING;"
echo ""
echo "-- 2. Resetar contador de chats de hoje para teste"
echo "UPDATE ai_usage_tracking"
echo "SET chat_requests = 0"
echo "WHERE user_id = 'eea9b847-2e26-42a9-b620-43cff6494472'"
echo "AND date = CURRENT_DATE;"