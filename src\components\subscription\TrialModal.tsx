import { AnimatePresence, motion } from "framer-motion";
import { X } from "lucide-react";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";

import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useAIQuota } from "@/hooks/useAIQuota";
import { useSubscription } from "@/hooks/useSubscription";

interface TrialModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const TrialModal = ({ isOpen, onClose }: TrialModalProps) => {
  const { trial, subscription } = useSubscription();
  const navigate = useNavigate();
  const { quotas } = useAIQuota();

  const trialInfo = useMemo(() => {
    if (!trial.isTrialing || trial.isExpired) return null;

    const now = new Date();
    const endDate = trial.endDate;
    const startDate = subscription
      ? new Date(subscription.current_period_start)
      : new Date();

    const totalDays = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const remainingMs = endDate.getTime() - now.getTime();
    const remainingDays = Math.max(
      0,
      Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
    );
    const remainingHours = Math.max(
      0,
      Math.ceil(remainingMs / (1000 * 60 * 60))
    );

    const progress = ((totalDays - remainingDays) / totalDays) * 100;
    const isExpiringSoon = remainingDays <= 2;
    const isLastDay = remainingDays <= 1;

    return {
      totalDays,
      remainingDays,
      remainingHours,
      progress,
      isExpiringSoon,
      isLastDay,
      endDate: endDate.toLocaleDateString("pt-BR"),
    };
  }, [trial, subscription]);

  const handleUpgrade = () => {
    navigate("/subscription?plan=pro&source=trial");
    onClose();
  };

  if (!trialInfo) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-md mx-4"
          >
            <div className="bg-background border border-border rounded-xl shadow-2xl overflow-hidden">
              {/* Header */}
              <div className="bg-gradient-to-r from-orange-500 to-amber-500 p-6 text-white relative">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="absolute top-4 right-4 h-8 w-8 text-white hover:bg-white/20"
                >
                  <X className="h-4 w-4" />
                </Button>

                <div className="flex items-center gap-3">
                  ⭐
                  <div>
                    <h2 className="text-xl font-bold">Trial Gratuito</h2>
                    <p className="text-orange-100 text-sm">
                      Aproveite todas as funcionalidades
                    </p>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6 space-y-6">
                {/* Time remaining */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    ⏰
                    <span className="text-2xl font-bold text-orange-600">
                      {trialInfo.remainingDays} dias restantes
                    </span>
                  </div>
                  <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground">
                    📅 até {trialInfo.endDate}
                  </div>

                  <div className="mt-4">
                    <Progress value={trialInfo.progress} className="h-2" />
                  </div>
                </div>

                {/* Quotas */}
                <div>
                  <h3 className="font-semibold mb-3 flex items-center gap-2">
                    ✨ Quotas do Trial:
                  </h3>
                  <div className="space-y-2">
                    {Object.entries(quotas).map(([feature, quota]) => (
                      <div
                        key={feature}
                        className="flex items-center justify-between p-2 rounded-lg bg-muted/50"
                      >
                        <div className="flex items-center gap-2">
                          {feature === "chat" && "💬"}
                          {feature === "budget" && "📄"}
                          {feature === "contract" && "📋"}
                          {feature === "sinapi" && "🗄️"}
                          <span className="text-sm font-medium">
                            {feature === "chat" && "Chat IA"}
                            {feature === "budget" && "Orçamentos"}
                            {feature === "contract" && "Contratos"}
                            {feature === "sinapi" && "SINAPI"}:
                          </span>
                        </div>
                        <div className="flex items-center gap-1 text-sm">
                          {quota.current}/
                          {quota.limit === -1 ? "∞" : quota.limit}{" "}
                          {quota.canUse ? "✅" : "❌"}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    ✨ Acesso limitado às funcionalidades de IA
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    ⏰ 7 dias de teste gratuito
                  </div>
                </div>

                {/* CTA */}
                <Button
                  onClick={handleUpgrade}
                  className="w-full bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white"
                >
                  Ver Planos Premium →
                </Button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};
