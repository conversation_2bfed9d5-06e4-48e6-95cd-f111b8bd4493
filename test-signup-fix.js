import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://anrphijuostbgbscxmzx.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testSignupFix() {
  console.log('🧪 Testando criação de usuário após correção...')
  
  const testEmail = `test-fix-${Date.now()}@example.com`
  const { data: signupData, error: signupError } = await supabase.auth.signUp({
    email: testEmail,
    password: 'TestPassword123!',
    options: {
      data: {
        first_name: 'Test',
        last_name: 'Fix'
      }
    }
  })

  if (signupError) {
    console.error('❌ Ainda há erro no signup:', signupError)
    return false
  } else {
    console.log('✅ Signup funcionou! ID do usuário:', signupData.user?.id)
    
    // Verificar se o trial foi criado
    if (signupData.user?.id) {
      console.log('🔍 Verificando se o trial foi criado...')
      
      // Aguardar um pouco para o trigger executar
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Verificar subscription
      const { data: subscription, error: subError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', signupData.user.id)
        .single()
      
      if (subError) {
        console.log('⚠️ Trial não foi criado automaticamente:', subError.message)
      } else {
        console.log('✅ Trial criado com sucesso:', subscription)
      }
    }
    
    return true
  }
}

testSignupFix().then(success => {
  if (success) {
    console.log('\n🎉 Problema de signup resolvido!')
  } else {
    console.log('\n❌ Problema ainda persiste. Verifique os logs do Supabase.')
  }
}).catch(console.error)
