// ============================================================================
// COMPONENTE: NotificationSettings - CONFIGURAÇÕES DE NOTIFICAÇÃO OBRASAI
// ============================================================================
// Componente refatorado para usar o sistema real de notificações
// Integração: useNotifications hook + Preferências reais do usuário
// ============================================================================

import {
  Bell, 
  CheckCircle,
  Mail, 
  Settings,
  Smartphone, 
  TestTube,
  Volume2, 
  VolumeX} from "lucide-react";
import { useEffect,useState } from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import type { NotificationChannel, NotificationType } from "@/hooks/useNotifications";
import { useNotifications } from "@/hooks/useNotifications";

// ============================================================================
// TIPOS E CONFIGURAÇÕES
// ============================================================================

interface NotificationTypeConfig {
  enabled: boolean;
  channels: NotificationChannel[];
  [key: string]: unknown;
}

interface NotificationPreferencesState {
  enabled: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone: string;
  preferences: Record<NotificationType, NotificationTypeConfig>;
  email_digest_frequency: 'never' | 'daily' | 'weekly';
  email_digest_time: string;
}

const notificationTypeLabels: Record<NotificationType, string> = {
  'obra_prazo_vencendo': 'Prazos de obras vencendo',
  'obra_orcamento_excedido': 'Orçamento de obra excedido',
  'obra_status_alterado': 'Status de obra alterado',
  'novo_lead_capturado': 'Novos leads capturados',
  'contrato_assinado': 'Contratos assinados',
  'contrato_vencendo': 'Contratos vencendo',
  'ia_analise_pronta': 'Análises de IA finalizadas',
  'ia_orcamento_gerado': 'Orçamentos IA gerados',
  'sinapi_atualizado': 'Atualizações SINAPI',
  'sistema_manutencao': 'Manutenções do sistema',
  'pagamento_vencendo': 'Pagamentos vencendo',
  'pagamento_processado': 'Pagamentos processados'
};

const notificationTypeDescriptions: Record<NotificationType, string> = {
  'obra_prazo_vencendo': 'Alertas quando obras estão próximas do prazo final',
  'obra_orcamento_excedido': 'Avisos quando gastos excedem o orçamento planejado',
  'obra_status_alterado': 'Notificações sobre mudanças no status das obras',
  'novo_lead_capturado': 'Alertas imediatos sobre novos leads via chatbot',
  'contrato_assinado': 'Confirmações de assinatura de contratos',
  'contrato_vencendo': 'Lembretes de contratos próximos ao vencimento',
  'ia_analise_pronta': 'Notificações quando análises de IA são concluídas',
  'ia_orcamento_gerado': 'Avisos sobre orçamentos gerados automaticamente',
  'sinapi_atualizado': 'Informações sobre atualizações da base SINAPI',
  'sistema_manutencao': 'Avisos sobre manutenções programadas',
  'pagamento_vencendo': 'Lembretes de pagamentos a vencer',
  'pagamento_processado': 'Confirmações de pagamentos processados'
};

const notificationTypeCategories = {
  obras: ['obra_prazo_vencendo', 'obra_orcamento_excedido', 'obra_status_alterado'] as NotificationType[],
  leads: ['novo_lead_capturado'] as NotificationType[],
  contratos: ['contrato_assinado', 'contrato_vencendo'] as NotificationType[],
  ia: ['ia_analise_pronta', 'ia_orcamento_gerado'] as NotificationType[],
  sistema: ['sinapi_atualizado', 'sistema_manutencao'] as NotificationType[],
  financeiro: ['pagamento_vencendo', 'pagamento_processado'] as NotificationType[]
};

const channelLabels: Record<NotificationChannel, string> = {
  'in_app': 'No aplicativo',
  'email': 'Por email',
  'push': 'Push notification',
  'sms': 'SMS'
};

const channelIcons: Record<NotificationChannel, React.ComponentType<{ className?: string }>> = {
  'in_app': Bell,
  'email': Mail,
  'push': Smartphone,
  'sms': Smartphone
};

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export function NotificationSettings() {
  const {
    preferences,
    updatePreferences,
    isUpdatingPreferences,
    sendTestNotification,
    unreadCount
  } = useNotifications();

  const [localPrefs, setLocalPrefs] = useState<NotificationPreferencesState | null>(null);

  // ============================================================================
  // EFFECTS E INICIALIZAÇÃO
  // ============================================================================

  useEffect(() => {
    if (preferences) {
      setLocalPrefs({
        enabled: preferences.enabled,
        quiet_hours_start: preferences.quiet_hours_start,
        quiet_hours_end: preferences.quiet_hours_end,
        timezone: preferences.timezone,
        preferences: preferences.preferences,
        email_digest_frequency: preferences.email_digest_frequency,
        email_digest_time: preferences.email_digest_time
      });
    }
  }, [preferences]);

  // ============================================================================
  // HANDLERS DE EVENTOS
  // ============================================================================

  const handleGlobalToggle = async (enabled: boolean) => {
    if (!localPrefs) return;

    const newPrefs = { ...localPrefs, enabled };
    setLocalPrefs(newPrefs);

    try {
      await updatePreferences(newPrefs);
    } catch (error) {
      console.error('Erro ao atualizar configuração global:', error);
      // Reverter estado local em caso de erro
      setLocalPrefs(prev => prev ? { ...prev, enabled: !enabled } : null);
    }
  };

  const handleTypeToggle = async (type: NotificationType, enabled: boolean) => {
    if (!localPrefs) return;

    const newPrefs = {
      ...localPrefs,
      preferences: {
        ...localPrefs.preferences,
        [type]: {
          ...localPrefs.preferences[type],
          enabled
        }
      }
    };
    setLocalPrefs(newPrefs);

    try {
      await updatePreferences(newPrefs);
    } catch (error) {
      console.error('Erro ao atualizar tipo de notificação:', error);
      // Reverter estado local
      setLocalPrefs(prev => prev ? {
        ...prev,
        preferences: {
          ...prev.preferences,
          [type]: {
            ...prev.preferences[type],
            enabled: !enabled
          }
        }
      } : null);
    }
  };

  const handleChannelToggle = async (type: NotificationType, channel: NotificationChannel, enabled: boolean) => {
    if (!localPrefs) return;

    const currentChannels = localPrefs.preferences[type]?.channels || [];
    const newChannels = enabled
      ? [...currentChannels, channel]
      : currentChannels.filter(c => c !== channel);

    const newPrefs = {
      ...localPrefs,
      preferences: {
        ...localPrefs.preferences,
        [type]: {
          ...localPrefs.preferences[type],
          channels: newChannels
        }
      }
    };
    setLocalPrefs(newPrefs);

    try {
      await updatePreferences(newPrefs);
    } catch (error) {
      console.error('Erro ao atualizar canais:', error);
      // Reverter estado local
      setLocalPrefs(prev => prev ? {
        ...prev,
        preferences: {
          ...prev.preferences,
          [type]: {
            ...prev.preferences[type],
            channels: currentChannels
          }
        }
      } : null);
    }
  };

  const handleQuietHoursToggle = async (enabled: boolean) => {
    if (!localPrefs) return;

    const newPrefs = {
      ...localPrefs,
      quiet_hours_start: enabled ? localPrefs.quiet_hours_start || '22:00' : undefined,
      quiet_hours_end: enabled ? localPrefs.quiet_hours_end || '08:00' : undefined
    };
    setLocalPrefs(newPrefs);

    try {
      await updatePreferences(newPrefs);
    } catch (error) {
      console.error('Erro ao atualizar horário silencioso:', error);
    }
  };

  const handleQuietHoursTimeChange = async (field: 'start' | 'end', time: string) => {
    if (!localPrefs) return;

    const newPrefs = {
      ...localPrefs,
      [`quiet_hours_${field}`]: time
    };
    setLocalPrefs(newPrefs);

    try {
      await updatePreferences(newPrefs);
    } catch (error) {
      console.error('Erro ao atualizar horário:', error);
    }
  };

  const handleDigestFrequencyChange = async (frequency: 'never' | 'daily' | 'weekly') => {
    if (!localPrefs) return;

    const newPrefs = { ...localPrefs, email_digest_frequency: frequency };
    setLocalPrefs(newPrefs);

    try {
      await updatePreferences(newPrefs);
    } catch (error) {
      console.error('Erro ao atualizar frequência de digest:', error);
    }
  };

  const handleTestNotification = async () => {
    try {
      await sendTestNotification();
      toast.success('Notificação de teste enviada!');
    } catch (error) {
      console.error('Erro ao enviar notificação de teste:', error);
      toast.error('Erro ao enviar notificação de teste');
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  if (!localPrefs) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <div className="text-center space-y-2">
              <Settings className="h-8 w-8 text-muted-foreground mx-auto animate-spin" />
              <p className="text-sm text-muted-foreground">Carregando configurações...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com informações gerais */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Configurações Gerais
              </CardTitle>
              <CardDescription>
                Gerencie como e quando você recebe notificações do ObraVision
              </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              {unreadCount > 0 && (
                <Badge variant="destructive" className="animate-pulse">
                  {unreadCount} não lidas
                </Badge>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleTestNotification}
                className="gap-2"
              >
                <TestTube className="h-4 w-4" />
                Teste
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Notificações habilitadas</Label>
              <p className="text-sm text-muted-foreground">
                Ativar ou desativar todas as notificações do sistema
              </p>
            </div>
            <Switch
              checked={localPrefs.enabled}
              onCheckedChange={handleGlobalToggle}
              disabled={isUpdatingPreferences}
            />
          </div>
        </CardContent>
      </Card>

      {/* Configurações por categoria */}
      {localPrefs.enabled && (
        <>
          {Object.entries(notificationTypeCategories).map(([category, types]) => (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="capitalize">
                  {category === 'ia' ? 'Inteligência Artificial' : category}
                </CardTitle>
                <CardDescription>
                  Configure notificações específicas para {category}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {types.map((type) => {
                  const typeConfig = localPrefs.preferences[type];
                  
                  return (
                    <div key={type} className="space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1 flex-1">
                          <div className="flex items-center gap-2">
                            <Label className="text-sm font-medium">
                              {notificationTypeLabels[type]}
                            </Label>
                            {typeConfig?.enabled && (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {notificationTypeDescriptions[type]}
                          </p>
                        </div>
                        <Switch
                          checked={typeConfig?.enabled || false}
                          onCheckedChange={(enabled) => handleTypeToggle(type, enabled)}
                          disabled={isUpdatingPreferences}
                        />
                      </div>

                      {typeConfig?.enabled && (
                        <div className="ml-6 space-y-2">
                          <Label className="text-xs font-medium text-muted-foreground">
                            Canais de envio:
                          </Label>
                          <div className="flex flex-wrap gap-2">
                            {(['in_app', 'email', 'push'] as NotificationChannel[]).map((channel) => {
                              const Icon = channelIcons[channel];
                              const isEnabled = typeConfig.channels?.includes(channel) || false;
                              
                              return (
                                <div
                                  key={channel}
                                  className={`flex items-center gap-2 px-3 py-1 rounded-md border cursor-pointer transition-colors ${
                                    isEnabled
                                      ? 'bg-primary/10 border-primary text-primary'
                                      : 'bg-muted border-border text-muted-foreground hover:bg-muted/80'
                                  }`}
                                  onClick={() => handleChannelToggle(type, channel, !isEnabled)}
                                >
                                  <Checkbox
                                    checked={isEnabled}
                                    onChange={() => {}}
                                    className="pointer-events-none"
                                  />
                                  <Icon className="h-3 w-3" />
                                  <span className="text-xs">{channelLabels[channel]}</span>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          ))}

          {/* Horário silencioso */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Volume2 className="h-5 w-5" />
                Horário Silencioso
              </CardTitle>
              <CardDescription>
                Configure um período sem notificações para não ser interrompido
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Ativar horário silencioso</Label>
                  <p className="text-sm text-muted-foreground">
                    Notificações serão pausadas durante este período
                  </p>
                </div>
                <Switch
                  checked={!!localPrefs.quiet_hours_start}
                  onCheckedChange={handleQuietHoursToggle}
                  disabled={isUpdatingPreferences}
                />
              </div>

              {localPrefs.quiet_hours_start && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Início</Label>
                    <Input
                      type="time"
                      value={localPrefs.quiet_hours_start}
                      onChange={(e) => handleQuietHoursTimeChange('start', e.target.value)}
                      disabled={isUpdatingPreferences}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Fim</Label>
                    <Input
                      type="time"
                      value={localPrefs.quiet_hours_end || '08:00'}
                      onChange={(e) => handleQuietHoursTimeChange('end', e.target.value)}
                      disabled={isUpdatingPreferences}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Digest de email */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Resumo por Email
              </CardTitle>
              <CardDescription>
                Configure o envio de resumos periódicos por email
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Frequência do resumo</Label>
                <Select
                  value={localPrefs.email_digest_frequency}
                  onValueChange={(value: 'never' | 'daily' | 'weekly') => 
                    handleDigestFrequencyChange(value)
                  }
                  disabled={isUpdatingPreferences}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="never">Nunca</SelectItem>
                    <SelectItem value="daily">Diariamente</SelectItem>
                    <SelectItem value="weekly">Semanalmente</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {localPrefs.email_digest_frequency !== 'never' && (
                <div className="space-y-2">
                  <Label>Horário de envio</Label>
                  <Input
                    type="time"
                    value={localPrefs.email_digest_time}
                    onChange={(e) => setLocalPrefs({
                      ...localPrefs,
                      email_digest_time: e.target.value
                    })}
                    disabled={isUpdatingPreferences}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {/* Estado desabilitado */}
      {!localPrefs.enabled && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-32 text-center space-y-2">
            <VolumeX className="h-8 w-8 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Notificações desabilitadas. Ative acima para configurar suas preferências.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}