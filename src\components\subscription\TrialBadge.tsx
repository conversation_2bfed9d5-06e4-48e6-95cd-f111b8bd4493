import { motion } from "framer-motion";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useSubscription } from "@/hooks/useSubscription";
import { TrialModal } from "./TrialModal";

export const TrialBadge = () => {
  const { trial } = useSubscription();
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (!trial.isTrialing || trial.isExpired) return null;

  return (
    <>
      <motion.div
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.3, duration: 0.3 }}
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsModalOpen(true)}
          className="h-8 px-3 rounded-full hover:bg-orange-50 dark:hover:bg-orange-950/20 transition-colors"
        >
          <Badge
            variant="secondary"
            className="bg-gradient-to-r from-orange-100 to-amber-100 dark:from-orange-950/50 dark:to-amber-950/50 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-800 hover:shadow-sm transition-shadow"
          >
            ⭐ Trial {trial.daysRemaining}d ⏰
          </Badge>
        </Button>
      </motion.div>

      <TrialModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </>
  );
};
