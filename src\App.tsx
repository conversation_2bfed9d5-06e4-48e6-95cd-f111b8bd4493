import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HelmetProvider } from "react-helmet-async";
import { Outlet, Route, Routes } from "react-router-dom";

import PatternsShowcase from "@/components/showcase/PatternsShowcase";
import { Toaster } from "@/components/ui/sonner";
import { AuthProvider } from "@/contexts/auth/AuthContext";
import { ProtectedRoute } from "@/contexts/auth/ProtectedRoutes";
import { LoadingProvider } from "@/contexts/LoadingContext";
// Vendas
import AuthCallback from "@/pages/AuthCallback";
// Admin - Dashboard de Métricas (apenas para administradores do sistema)
// SINAPI
import Dashboard from "@/pages/Dashboard";
// Chat AI
import ChatAIPage from "@/pages/dashboard/ai/Chat";
// IA
import AlertasAvancados from "@/pages/dashboard/AlertasAvancados";
// Construtoras/Autônomos
import ConstrutorasLista from "@/pages/dashboard/construtoras/ConstrutorasLista";
import EditarConstrutora from "@/pages/dashboard/construtoras/EditarConstrutora";
import NovaConstrutora from "@/pages/dashboard/construtoras/NovaConstrutora";
import ContratoComIAPremium from "@/pages/dashboard/contratos/ContratoComIAPremium";
import ContratoDetalhe from "@/pages/dashboard/contratos/ContratoDetalhe";
// Contratos
import ContratosLista from "@/pages/dashboard/contratos/ContratosLista";
// Controle Orçamentário
import ControleOrcamentario from "@/pages/dashboard/ControleOrcamentario";
// Despesas
import DespesasLista from "@/pages/dashboard/despesas/DespesasLista";
import EditarDespesa from "@/pages/dashboard/despesas/EditarDespesa";
import NovaDespesa from "@/pages/dashboard/despesas/NovaDespesa";
// Fornecedores
import EditarFornecedor from "@/pages/dashboard/fornecedores/EditarFornecedor";
import FornecedoresPFLista from "@/pages/dashboard/fornecedores/FornecedoresPFLista";
import FornecedoresPJLista from "@/pages/dashboard/fornecedores/FornecedoresPJLista";
import NovoFornecedor from "@/pages/dashboard/fornecedores/NovoFornecedor";
// Licitações
// import BuscarLicitacoes from "@/pages/dashboard/licitacoes/BuscarLicitacoes";
// import LicitacaoDetalhes from "@/pages/dashboard/licitacoes/LicitacaoDetalhes";
// import PerfilConstrutora from "@/pages/dashboard/licitacoes/PerfilConstrutora";
// Notas Fiscais
import EditarObra from "@/pages/dashboard/obras/EditarObra";
import NovaObraRefactored from "@/pages/dashboard/obras/NovaObraRefactored";
import NovoCondominioPage from "@/pages/dashboard/obras/NovoCondominio";
import ObraDetalhe from "@/pages/dashboard/obras/ObraDetalhe";
// Obras
import ObrasLista from "@/pages/dashboard/obras/ObrasLista";
// Orçamento Paramétrico
import { NovoOrcamento } from "@/pages/dashboard/orcamento/NovoOrcamento";
import OrcamentoDetalhe from "@/pages/dashboard/orcamento/OrcamentoDetalhe";
import OrcamentosLista from "@/pages/dashboard/orcamento/OrcamentosLista";
// PlantaIA
// import PlantaIA from "@/pages/dashboard/planta-ia/PlantaIA";
import VendaDetalhe from "@/pages/dashboard/vendas/VendaDetalhe";
import VendasLista from "@/pages/dashboard/vendas/VendasLista";
import ForgotPassword from "@/pages/ForgotPassword";
import Index from "@/pages/Index";
import Login from "@/pages/Login";
import NotFound from "@/pages/NotFound";
import Register from "@/pages/Register";
// Settings
import Settings from "@/pages/Settings";
import { ThemeProvider } from "@/providers/theme-provider";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutos - dados não são considerados stale por 5 min
      gcTime: 10 * 60 * 1000, // 10 minutos - cache mantido por 10 min
      refetchOnReconnect: "always",
      refetchOnMount: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

function AppContent() {
  return (
    <>
      <Outlet />
    </>
  );
}

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
        <LoadingProvider>
          <AuthProvider>
            <HelmetProvider>
              <Routes>
                <Route element={<AppContent />}>
                  {/* Rotas Públicas */}
                  <Route path="/" element={<Index />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/forgot-password" element={<ForgotPassword />} />
                  <Route path="/auth/callback" element={<AuthCallback />} />
                  <Route path="/landing" element={<Index />} />
                  <Route path="/showcase" element={<PatternsShowcase />} />

                  {/* Rotas Protegidas */}
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <Dashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/obras"
                    element={
                      <ProtectedRoute>
                        <ObrasLista />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/obras/condominio/novo"
                    element={
                      <ProtectedRoute>
                        <NovoCondominioPage />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/obras/nova"
                    element={
                      <ProtectedRoute>
                        <NovaObraRefactored />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/obras/:id"
                    element={
                      <ProtectedRoute>
                        <ObraDetalhe />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/obras/:id/editar"
                    element={
                      <ProtectedRoute>
                        <EditarObra />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/dashboard/vendas"
                    element={
                      <ProtectedRoute>
                        <VendasLista />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/vendas/:id"
                    element={
                      <ProtectedRoute>
                        <VendaDetalhe />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/dashboard/despesas"
                    element={
                      <ProtectedRoute>
                        <DespesasLista />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/despesas/nova"
                    element={
                      <ProtectedRoute>
                        <NovaDespesa />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/despesas/:id/editar"
                    element={
                      <ProtectedRoute>
                        <EditarDespesa />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/dashboard/construtoras"
                    element={
                      <ProtectedRoute>
                        <ConstrutorasLista />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/construtoras/nova"
                    element={
                      <ProtectedRoute>
                        <NovaConstrutora />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/construtoras/:id/editar"
                    element={
                      <ProtectedRoute>
                        <EditarConstrutora />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/dashboard/orcamentos"
                    element={
                      <ProtectedRoute>
                        <OrcamentosLista />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/orcamentos/novo"
                    element={
                      <ProtectedRoute>
                        <NovoOrcamento />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/orcamentos/:id"
                    element={
                      <ProtectedRoute>
                        <OrcamentoDetalhe />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/dashboard/contratos"
                    element={
                      <ProtectedRoute>
                        <ContratosLista />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/contratos/novo"
                    element={
                      <ProtectedRoute>
                        <ContratoComIAPremium />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/contratos/:id"
                    element={
                      <ProtectedRoute>
                        <ContratoDetalhe />
                      </ProtectedRoute>
                    }
                  />

                  {/* <Route
                    path="/dashboard/licitacoes"
                    element={
                      <ProtectedRoute>
                        <BuscarLicitacoes />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/licitacoes/perfil-construtora"
                    element={
                      <ProtectedRoute>
                        <PerfilConstrutora />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/licitacoes/:id"
                    element={
                      <ProtectedRoute>
                        <LicitacaoDetalhes />
                      </ProtectedRoute>
                    }
                  /> */}

                  <Route
                    path="/dashboard/alertas"
                    element={
                      <ProtectedRoute>
                        <AlertasAvancados />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/chat"
                    element={
                      <ProtectedRoute>
                        <ChatAIPage />
                      </ProtectedRoute>
                    }
                  />
                  {/* <Route
                    path="/dashboard/planta-ia"
                    element={
                      <ProtectedRoute>
                        <PlantaIA />
                      </ProtectedRoute>
                    }
                  /> */}
                  <Route
                    path="/dashboard/controle-orcamentario"
                    element={
                      <ProtectedRoute>
                        <ControleOrcamentario />
                      </ProtectedRoute>
                    }
                  />

                  <Route
                    path="/dashboard/fornecedores/pj"
                    element={
                      <ProtectedRoute>
                        <FornecedoresPJLista />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/fornecedores/pf"
                    element={
                      <ProtectedRoute>
                        <FornecedoresPFLista />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/fornecedores/novo"
                    element={
                      <ProtectedRoute>
                        <NovoFornecedor />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/fornecedores/pj/:id/editar"
                    element={
                      <ProtectedRoute>
                        <EditarFornecedor />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/dashboard/fornecedores/pf/:id/editar"
                    element={
                      <ProtectedRoute>
                        <EditarFornecedor />
                      </ProtectedRoute>
                    }
                  />


                  <Route
                    path="/settings"
                    element={
                      <ProtectedRoute>
                        <Settings />
                      </ProtectedRoute>
                    }
                  />

                  {/* Rota de fallback */}
                  <Route path="*" element={<NotFound />} />
                </Route>
              </Routes>
              <Toaster position="top-center" />
            </HelmetProvider>
          </AuthProvider>
        </LoadingProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
