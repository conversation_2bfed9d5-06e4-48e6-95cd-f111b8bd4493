import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://anrphijuostbgbscxmzx.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTk0NzQ5NywiZXhwIjoyMDYxNTIzNDk3fQ.y_wdHHgs1O4KkvonDDUUGlPGr0K42cMEXVYrmB7JolE'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function debugSignupError() {
  console.log('🔍 Verificando triggers na tabela auth.users...')
  
  // 1. Verificar triggers na tabela auth.users
  const { data: triggers, error: triggersError } = await supabase
    .rpc('sql', {
      query: `
        SELECT 
          trigger_name,
          event_manipulation,
          action_timing,
          action_statement
        FROM information_schema.triggers 
        WHERE event_object_table = 'users' 
        AND event_object_schema = 'auth'
        ORDER BY trigger_name;
      `
    })

  if (triggersError) {
    console.error('❌ Erro ao buscar triggers:', triggersError)
  } else {
    console.log('📋 Triggers encontrados:', triggers)
  }

  // 2. Verificar se as tabelas necessárias existem
  console.log('\n🔍 Verificando tabelas necessárias...')
  
  const tables = ['subscriptions', 'ai_trial_usage', 'notification_preferences']
  
  for (const table of tables) {
    const { data, error } = await supabase
      .from(table)
      .select('*')
      .limit(1)
    
    if (error) {
      console.error(`❌ Erro na tabela ${table}:`, error.message)
    } else {
      console.log(`✅ Tabela ${table} existe e está acessível`)
    }
  }

  // 3. Verificar constraints da tabela subscriptions
  console.log('\n🔍 Verificando constraints da tabela subscriptions...')
  
  const { data: constraints, error: constraintsError } = await supabase
    .rpc('sql', {
      query: `
        SELECT 
          constraint_name,
          constraint_type,
          table_name
        FROM information_schema.table_constraints 
        WHERE table_name = 'subscriptions' 
        AND table_schema = 'public'
        ORDER BY constraint_name;
      `
    })

  if (constraintsError) {
    console.error('❌ Erro ao buscar constraints:', constraintsError)
  } else {
    console.log('📋 Constraints encontradas:', constraints)
  }

  // 4. Tentar criar um usuário de teste para ver o erro específico
  console.log('\n🔍 Testando criação de usuário...')
  
  const testEmail = `test-${Date.now()}@example.com`
  const { data: signupData, error: signupError } = await supabase.auth.signUp({
    email: testEmail,
    password: 'TestPassword123!',
    options: {
      data: {
        first_name: 'Test',
        last_name: 'User'
      }
    }
  })

  if (signupError) {
    console.error('❌ Erro no signup:', signupError)
  } else {
    console.log('✅ Signup bem-sucedido:', signupData.user?.id)
    
    // Limpar usuário de teste
    if (signupData.user?.id) {
      await supabase.auth.admin.deleteUser(signupData.user.id)
      console.log('🧹 Usuário de teste removido')
    }
  }
}

debugSignupError().catch(console.error)
