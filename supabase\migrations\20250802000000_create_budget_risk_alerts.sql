-- =====================================================
-- Migração: Sistema de Alertas de Risco Orçamentário
-- Data: 2025-08-02
-- Versão: 1.0.0
-- =====================================================

-- Criar tabela para registrar alertas de risco orçamentário
CREATE TABLE budget_risk_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  obra_id UUID REFERENCES obras(id) ON DELETE CASCADE,
  usuario_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  
  -- Dados do orçamento e análise
  valor_investido NUMERIC NOT NULL CHECK (valor_investido > 0),
  valor_estimado_ia NUMERIC NOT NULL CHECK (valor_estimado_ia > 0),
  percentual_diferenca NUMERIC NOT NULL,
  nivel_risco TEXT NOT NULL CHECK (nivel_risco IN ('low', 'medium', 'high', 'critical')),
  
  -- Parâmetros da análise
  tipo_obra TEXT NOT NULL,
  padrao_obra TEXT NOT NULL,
  area_total NUMERIC NOT NULL CHECK (area_total > 0),
  estado TEXT NOT NULL,
  cidade TEXT,
  
  -- Ação do usuário
  aceito_pelo_usuario BOOLEAN DEFAULT false,
  valor_aceito NUMERIC, -- Valor que o usuário decidiu usar
  acao_usuario TEXT CHECK (acao_usuario IN ('accepted_suggestion', 'kept_original', 'custom_value', 'dismissed')),
  observacoes_usuario TEXT,
  
  -- Dados técnicos da análise
  calculation_params JSONB, -- Parâmetros detalhados do cálculo
  edge_function_version TEXT DEFAULT 'validate-budget-risk-1.0.0',
  
  -- Metadados
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_percentage_difference CHECK (percentual_diferenca >= -100),
  CONSTRAINT valid_user_action CHECK (
    (aceito_pelo_usuario = true AND acao_usuario IS NOT NULL) OR 
    (aceito_pelo_usuario = false AND acao_usuario IS NULL)
  )
);

-- Índices para performance
CREATE INDEX idx_budget_risk_alerts_obra_id ON budget_risk_alerts(obra_id);
CREATE INDEX idx_budget_risk_alerts_usuario_id ON budget_risk_alerts(usuario_id);
CREATE INDEX idx_budget_risk_alerts_tenant_id ON budget_risk_alerts(tenant_id);
CREATE INDEX idx_budget_risk_alerts_nivel_risco ON budget_risk_alerts(nivel_risco);
CREATE INDEX idx_budget_risk_alerts_tipo_obra ON budget_risk_alerts(tipo_obra);
CREATE INDEX idx_budget_risk_alerts_created_at ON budget_risk_alerts(created_at DESC);

-- Índice composto para queries comuns
CREATE INDEX idx_budget_risk_alerts_tenant_obra ON budget_risk_alerts(tenant_id, obra_id);
CREATE INDEX idx_budget_risk_alerts_tenant_risk ON budget_risk_alerts(tenant_id, nivel_risco, created_at DESC);

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_budget_risk_alerts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_budget_risk_alerts_updated_at
  BEFORE UPDATE ON budget_risk_alerts
  FOR EACH ROW
  EXECUTE FUNCTION update_budget_risk_alerts_updated_at();

-- =====================================================
-- RLS (Row Level Security)
-- =====================================================

-- Habilitar RLS na tabela
ALTER TABLE budget_risk_alerts ENABLE ROW LEVEL SECURITY;

-- Política para usuários autenticados verem apenas dados do seu tenant
CREATE POLICY budget_risk_alerts_tenant_isolation 
ON budget_risk_alerts 
FOR ALL 
TO authenticated 
USING (
  tenant_id IN (
    SELECT tenant_id 
    FROM user_tenants 
    WHERE user_id = auth.uid()
  )
);

-- Política adicional para garantir que apenas o proprietário da obra veja os alertas
CREATE POLICY budget_risk_alerts_obra_owner 
ON budget_risk_alerts 
FOR ALL 
TO authenticated 
USING (
  obra_id IN (
    SELECT id 
    FROM obras 
    WHERE tenant_id IN (
      SELECT tenant_id 
      FROM user_tenants 
      WHERE user_id = auth.uid()
    )
  )
);

-- =====================================================
-- FUNÇÃO PARA REGISTRAR ALERTA DE RISCO
-- =====================================================

CREATE OR REPLACE FUNCTION registrar_alerta_risco_orcamento(
  p_obra_id UUID,
  p_valor_investido NUMERIC,
  p_valor_estimado_ia NUMERIC,
  p_percentual_diferenca NUMERIC,
  p_nivel_risco TEXT,
  p_tipo_obra TEXT,
  p_padrao_obra TEXT,
  p_area_total NUMERIC,
  p_estado TEXT,
  p_cidade TEXT DEFAULT NULL,
  p_calculation_params JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_tenant_id UUID;
  v_alert_id UUID;
BEGIN
  -- Verificar se o usuário está autenticado
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'Usuário não autenticado';
  END IF;

  -- Buscar tenant_id do usuário
  SELECT tenant_id INTO v_tenant_id
  FROM user_tenants
  WHERE user_id = auth.uid()
  LIMIT 1;

  IF v_tenant_id IS NULL THEN
    RAISE EXCEPTION 'Tenant não encontrado para o usuário';
  END IF;

  -- Verificar se a obra pertence ao tenant do usuário
  IF NOT EXISTS (
    SELECT 1 FROM obras 
    WHERE id = p_obra_id AND tenant_id = v_tenant_id
  ) THEN
    RAISE EXCEPTION 'Obra não encontrada ou sem permissão de acesso';
  END IF;

  -- Inserir o alerta
  INSERT INTO budget_risk_alerts (
    obra_id,
    usuario_id,
    tenant_id,
    valor_investido,
    valor_estimado_ia,
    percentual_diferenca,
    nivel_risco,
    tipo_obra,
    padrao_obra,
    area_total,
    estado,
    cidade,
    calculation_params
  )
  VALUES (
    p_obra_id,
    auth.uid(),
    v_tenant_id,
    p_valor_investido,
    p_valor_estimado_ia,
    p_percentual_diferenca,
    p_nivel_risco,
    p_tipo_obra,
    p_padrao_obra,
    p_area_total,
    p_estado,
    p_cidade,
    p_calculation_params
  )
  RETURNING id INTO v_alert_id;

  RETURN v_alert_id;
END;
$$;

-- =====================================================
-- FUNÇÃO PARA ATUALIZAR AÇÃO DO USUÁRIO
-- =====================================================

CREATE OR REPLACE FUNCTION atualizar_acao_usuario_alerta(
  p_alert_id UUID,
  p_acao_usuario TEXT,
  p_valor_aceito NUMERIC DEFAULT NULL,
  p_observacoes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_tenant_id UUID;
BEGIN
  -- Verificar se o usuário está autenticado
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'Usuário não autenticado';
  END IF;

  -- Buscar tenant_id do usuário
  SELECT tenant_id INTO v_tenant_id
  FROM user_tenants
  WHERE user_id = auth.uid()
  LIMIT 1;

  IF v_tenant_id IS NULL THEN
    RAISE EXCEPTION 'Tenant não encontrado para o usuário';
  END IF;

  -- Verificar se o alerta pertence ao usuário/tenant
  IF NOT EXISTS (
    SELECT 1 FROM budget_risk_alerts 
    WHERE id = p_alert_id 
    AND tenant_id = v_tenant_id 
    AND usuario_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Alerta não encontrado ou sem permissão de acesso';
  END IF;

  -- Atualizar o alerta
  UPDATE budget_risk_alerts
  SET 
    aceito_pelo_usuario = true,
    acao_usuario = p_acao_usuario,
    valor_aceito = p_valor_aceito,
    observacoes_usuario = p_observacoes,
    updated_at = NOW()
  WHERE id = p_alert_id;

  RETURN true;
END;
$$;

-- =====================================================
-- VIEW PARA ESTATÍSTICAS DE ALERTAS
-- =====================================================

CREATE OR REPLACE VIEW budget_risk_alerts_stats AS
SELECT 
  tenant_id,
  COUNT(*) as total_alertas,
  COUNT(CASE WHEN nivel_risco = 'critical' THEN 1 END) as alertas_criticos,
  COUNT(CASE WHEN nivel_risco = 'high' THEN 1 END) as alertas_altos,
  COUNT(CASE WHEN nivel_risco = 'medium' THEN 1 END) as alertas_medios,
  COUNT(CASE WHEN aceito_pelo_usuario = true THEN 1 END) as alertas_aceitos,
  COUNT(CASE WHEN acao_usuario = 'accepted_suggestion' THEN 1 END) as sugestoes_aceitas,
  AVG(percentual_diferenca) as media_diferenca_percentual,
  AVG(CASE WHEN aceito_pelo_usuario = true THEN percentual_diferenca END) as media_diferenca_aceitos,
  DATE_TRUNC('month', created_at) as mes_referencia
FROM budget_risk_alerts
GROUP BY tenant_id, DATE_TRUNC('month', created_at)
ORDER BY mes_referencia DESC;

-- Comentários da tabela e colunas
COMMENT ON TABLE budget_risk_alerts IS 'Registra alertas de risco orçamentário quando o valor investido está significativamente abaixo da estimativa paramétrica';

COMMENT ON COLUMN budget_risk_alerts.valor_investido IS 'Valor que o usuário informou ter disponível para investir na obra';
COMMENT ON COLUMN budget_risk_alerts.valor_estimado_ia IS 'Valor estimado pela IA baseado em parâmetros técnicos e dados de mercado';
COMMENT ON COLUMN budget_risk_alerts.percentual_diferenca IS 'Percentual de diferença entre estimativa e investimento (positivo = estimativa maior)';
COMMENT ON COLUMN budget_risk_alerts.nivel_risco IS 'Nível de risco calculado: low, medium, high, critical';
COMMENT ON COLUMN budget_risk_alerts.aceito_pelo_usuario IS 'Se o usuário tomou alguma ação em resposta ao alerta';
COMMENT ON COLUMN budget_risk_alerts.acao_usuario IS 'Ação específica tomada pelo usuário';
COMMENT ON COLUMN budget_risk_alerts.calculation_params IS 'Parâmetros técnicos usados no cálculo da estimativa';

-- =====================================================
-- GRANTS DE PERMISSÃO
-- =====================================================

-- Permitir que usuários autenticados usem as funções
GRANT EXECUTE ON FUNCTION registrar_alerta_risco_orcamento TO authenticated;
GRANT EXECUTE ON FUNCTION atualizar_acao_usuario_alerta TO authenticated;

-- Permitir acesso à view de estatísticas
GRANT SELECT ON budget_risk_alerts_stats TO authenticated;

-- =====================================================
-- FINALIZAÇÃO
-- =====================================================

-- Log da migração
INSERT INTO supabase_migrations.schema_migrations (version, statements, name)
VALUES (
  '20250802000000',
  1,
  'create_budget_risk_alerts'
);