/**
 * Script de Teste do Sistema de Alerta de Risco Orçamentário
 * 
 * Testa o sistema com os dados reais da obra ODTWIN FRITSCHE FH:
 * - Orçamento investido: R$ 2.500.000,00
 * - Estimativa IA: R$ 7.919.122,48
 * - Diferença: 216% (CRÍTICO!)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Simular dados da obra ODTWIN FRITSCHE FH
const dadosObraReal = {
  nome: "ODTWIN FRITSCHE FH",
  tipo_obra: "R4_MULTIFAMILIAR",
  padrao_obra: "POPULAR",
  tipo_condominio: "VERTICAL",
  area_total: 7600,
  area_construida: 7600,
  estado: "GO",
  cidade: "Valparaíso de Goiás",
  orcamento_investido: 2500000.00,
  estimativa_ia_real: 7919122.48,
  numero_blocos: 1,
  andares_por_bloco: 10,
  unidades_por_andar: 8,
  numero_unidades: 80
};

// URL da Edge Function (ajustar conforme ambiente)
const EDGE_FUNCTION_URL = 'https://anrphijuostbgbscxmzx.supabase.co/functions/v1/validate-budget-risk';

/**
 * Testa a Edge Function de validação de risco
 */
async function testarEdgeFunction() {
  console.log('🧪 Testando Edge Function validate-budget-risk...\n');

  const requestData = {
    dados_obra: {
      tipo_obra: dadosObraReal.tipo_obra,
      padrao_obra: dadosObraReal.padrao_obra,
      area_total: dadosObraReal.area_total,
      area_construida: dadosObraReal.area_construida,
      estado: dadosObraReal.estado,
      cidade: dadosObraReal.cidade,
      tipo_condominio: dadosObraReal.tipo_condominio,
      numero_blocos: dadosObraReal.numero_blocos,
      andares_por_bloco: dadosObraReal.andares_por_bloco,
      unidades_por_andar: dadosObraReal.unidades_por_andar,
      numero_unidades: dadosObraReal.numero_unidades
    },
    orcamento_usuario: dadosObraReal.orcamento_investido
  };

  try {
    console.log('📤 Enviando request:', JSON.stringify(requestData, null, 2));
    
    const response = await fetch(EDGE_FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc'
      },
      body: JSON.stringify(requestData)
    });

    const result = await response.json();
    
    console.log('\n📥 Response da Edge Function:');
    console.log('Status:', response.status);
    console.log('Success:', result.success);
    
    if (result.success) {
      console.log('\n✅ Cálculo bem-sucedido!');
      console.log('💰 Custo estimado:', new Intl.NumberFormat('pt-BR', { 
        style: 'currency', 
        currency: 'BRL' 
      }).format(result.data.estimated_cost));
      
      console.log('💰 Custo por m²:', new Intl.NumberFormat('pt-BR', { 
        style: 'currency', 
        currency: 'BRL' 
      }).format(result.data.cost_per_m2));
      
      console.log('\n🚨 Análise de Risco:');
      console.log('- Nível de risco:', result.data.risk_analysis.risk_level);
      console.log('- Diferença percentual:', result.data.risk_analysis.percentage_difference + '%');
      console.log('- Subdimensionado?', result.data.risk_analysis.is_underbudgeted ? 'SIM' : 'NÃO');
      console.log('- Valor mínimo sugerido:', new Intl.NumberFormat('pt-BR', { 
        style: 'currency', 
        currency: 'BRL' 
      }).format(result.data.risk_analysis.suggested_minimum));
      
      console.log('\n⚙️ Parâmetros do Cálculo:');
      console.log('- Tipo de obra:', result.data.calculation_params.tipo_obra);
      console.log('- Área calculada:', result.data.calculation_params.area_calculada + ' m²');
      console.log('- Multiplicador regional:', result.data.calculation_params.multiplicador_regional);
      console.log('- Fator de complexidade:', result.data.calculation_params.fator_complexidade);
      console.log('- Versão do cálculo:', result.data.calculation_params.versao_calculo);
    } else {
      console.log('\n❌ Erro no cálculo:');
      console.log('Erro:', result.error);
    }
    
    if (result.debug) {
      console.log('\n🔍 Debug Info:');
      console.log(JSON.stringify(result.debug, null, 2));
    }
    
  } catch (error) {
    console.error('\n💥 Erro na requisição:', error.message);
  }
}

/**
 * Compara com o resultado real da obra
 */
function compararComResultadoReal() {
  console.log('\n\n📊 COMPARAÇÃO COM RESULTADO REAL DA OBRA:\n');
  
  console.log('🏗️ Obra: ' + dadosObraReal.nome);
  console.log('📍 Local: ' + dadosObraReal.cidade + ', ' + dadosObraReal.estado);
  console.log('📐 Área: ' + dadosObraReal.area_total.toLocaleString('pt-BR') + ' m²');
  console.log('🏠 Unidades: ' + dadosObraReal.numero_unidades);
  
  console.log('\n💰 VALORES:');
  console.log('- Orçamento investido (real):', new Intl.NumberFormat('pt-BR', { 
    style: 'currency', 
    currency: 'BRL' 
  }).format(dadosObraReal.orcamento_investido));
  
  console.log('- Estimativa IA (real):', new Intl.NumberFormat('pt-BR', { 
    style: 'currency', 
    currency: 'BRL' 
  }).format(dadosObraReal.estimativa_ia_real));
  
  const diferencaReal = dadosObraReal.estimativa_ia_real - dadosObraReal.orcamento_investido;
  const percentualReal = (diferencaReal / dadosObraReal.orcamento_investido) * 100;
  
  console.log('- Diferença:', new Intl.NumberFormat('pt-BR', { 
    style: 'currency', 
    currency: 'BRL' 
  }).format(diferencaReal));
  
  console.log('- Percentual de diferença:', percentualReal.toFixed(1) + '%');
  
  console.log('\n🚨 ANÁLISE:');
  if (percentualReal > 100) {
    console.log('⛔ RISCO CRÍTICO: Orçamento muito abaixo da estimativa!');
  } else if (percentualReal > 50) {
    console.log('🚨 RISCO ALTO: Orçamento significativamente abaixo da estimativa');
  } else if (percentualReal > 20) {
    console.log('⚠️ RISCO MÉDIO: Orçamento abaixo da estimativa');
  } else {
    console.log('✅ RISCO BAIXO: Orçamento adequado');
  }
}

/**
 * Simula o fluxo completo do usuário
 */
function simularFluxoUsuario() {
  console.log('\n\n🎭 SIMULAÇÃO DO FLUXO DO USUÁRIO:\n');
  
  console.log('1. 👤 Usuário acessa o formulário "Nova Obra"');
  console.log('2. ✍️ Preenche dados básicos:');
  console.log('   - Nome: ' + dadosObraReal.nome);
  console.log('   - Área: ' + dadosObraReal.area_total + ' m²');
  console.log('   - Estado: ' + dadosObraReal.estado);
  
  console.log('3. 💰 Informa orçamento disponível: ' + new Intl.NumberFormat('pt-BR', { 
    style: 'currency', 
    currency: 'BRL' 
  }).format(dadosObraReal.orcamento_investido));
  
  console.log('4. 🤖 Sistema detecta discrepância e executa Edge Function');
  console.log('5. 🚨 Alerta aparece automaticamente:');
  console.log('   "🔴 CRÍTICO: Orçamento 216% abaixo da estimativa. Provável inviabilidade."');
  
  console.log('6. 💡 Sistema sugere valor mínimo baseado na IA');
  console.log('7. 🤔 Usuário tem as opções:');
  console.log('   - "Usar Sugestão da IA" (recomendado)');
  console.log('   - "Revisar Dados" (verificar se área/tipo estão corretos)');
  console.log('   - "Continuar Mesmo Assim" (não recomendado)');
  
  console.log('\n💪 BENEFÍCIOS ALCANÇADOS:');
  console.log('✅ Prevenção de projeto inviável financeiramente');
  console.log('✅ Educação do usuário sobre custos reais');
  console.log('✅ Sugestão inteligente baseada em dados de mercado');
  console.log('✅ Transparência total no processo');
}

/**
 * Função principal de teste
 */
async function executarTestes() {
  console.log('🧪 TESTE DO SISTEMA DE ALERTA DE RISCO ORÇAMENTÁRIO');
  console.log('=' .repeat(60));
  
  // Comparar com resultado real
  compararComResultadoReal();
  
  // Testar Edge Function
  await testarEdgeFunction();
  
  // Simular fluxo do usuário
  simularFluxoUsuario();
  
  console.log('\n✅ Testes concluídos com sucesso!');
  console.log('\n' + '=' .repeat(60));
}

// Executar os testes
if (typeof window === 'undefined') {
  // Node.js - usar fetch nativo do Node 18+
  if (typeof fetch === 'undefined') {
    console.error('Fetch não disponível. Execute com Node.js 18+ ou instale node-fetch');
    process.exit(1);
  }
  executarTestes().catch(console.error);
} else {
  // Browser
  executarTestes().catch(console.error);
}