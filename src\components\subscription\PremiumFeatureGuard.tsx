import { motion } from "framer-motion";
import { <PERSON>, <PERSON>, Sparkles, Zap } from "lucide-react";
import type { ReactNode } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useSubscription } from "@/hooks/useSubscription";

interface PremiumFeatureGuardProps {
  children: ReactNode;
  feature: 'aiFeatures' | 'advancedAnalytics' | 'apiAccess' | 'prioritySupport';
  fallbackTitle?: string;
  fallbackDescription?: string;
}

export const PremiumFeatureGuard = ({ 
  children, 
  feature,
  fallbackTitle,
  fallbackDescription 
}: PremiumFeatureGuardProps) => {
  const { canUseFeature, currentPlan, upgradeUrl } = useSubscription();

  // Se pode usar a funcionalidade, renderizar normalmente
  if (canUseFeature[feature]) {
    return <>{children}</>;
  }

  // Configurações específicas por funcionalidade
  const featureConfig = {
    aiFeatures: {
      title: "IA Avançada - Exclusivo Premium",
      description: "Acesse a IA especializada em contratos, análise contextual avançada e sugestões inteligentes.",
      icon: Sparkles,
      requiredPlan: "Pro ou Enterprise"
    },
    advancedAnalytics: {
      title: "Analytics Avançados - Premium",
      description: "Relatórios detalhados, insights de performance e análises preditivas para suas obras.",
      icon: Zap,
      requiredPlan: "Pro ou Enterprise"
    },
    apiAccess: {
      title: "Acesso à API - Enterprise",
      description: "Integre o ObraVision com seus sistemas através da nossa API REST completa.",
      icon: Lock,
      requiredPlan: "Enterprise"
    },
    prioritySupport: {
      title: "Suporte Prioritário - Premium",
      description: "Atendimento prioritário via chat, email e telefone com SLA garantido.",
      icon: Crown,
      requiredPlan: "Pro ou Enterprise"
    }
  };

  const config = featureConfig[feature];
  const Icon = config.icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900 flex items-center justify-center p-6">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-2xl w-full"
      >
        <Card className="border-2 border-amber-200 dark:border-amber-700 bg-gradient-to-br from-white to-amber-50/50 dark:from-slate-800 dark:to-amber-900/20 backdrop-blur-sm shadow-2xl">
          <CardHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="flex justify-center"
            >
              <div className="h-20 w-20 rounded-full bg-gradient-to-br from-amber-400 to-orange-500 flex items-center justify-center shadow-lg">
                <Crown className="h-10 w-10 text-white" />
              </div>
            </motion.div>
            
            <div className="space-y-2">
              <CardTitle className="text-2xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                {fallbackTitle || config.title}
              </CardTitle>
              <CardDescription className="text-base text-slate-600 dark:text-slate-400">
                {fallbackDescription || config.description}
              </CardDescription>
            </div>

            <div className="flex justify-center gap-2">
              <Badge 
                variant="secondary" 
                className="bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 border-amber-300"
              >
                <Icon className="h-4 w-4 mr-1" />
                Funcionalidade Premium
              </Badge>
              <Badge 
                variant="outline" 
                className="border-slate-300 text-slate-600 dark:border-slate-600 dark:text-slate-400"
              >
                Plano Atual: {currentPlan}
              </Badge>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Benefícios */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg text-center">O que você ganha com o upgrade:</h3>
              
              <div className="grid gap-3">
                <motion.div
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
                >
                  <Sparkles className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium">IA Especializada em Contratos de Construção</span>
                </motion.div>
                
                <motion.div
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="flex items-center gap-3 p-3 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-200 dark:border-purple-800"
                >
                  <Zap className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  <span className="text-sm font-medium">Análise Contextual Avançada</span>
                </motion.div>
                
                <motion.div
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="flex items-center gap-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800"
                >
                  <Crown className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium">Templates Premium e Suporte Prioritário</span>
                </motion.div>
              </div>
            </div>

            {/* Plano necessário */}
            <div className="text-center p-4 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
              <p className="text-sm text-amber-800 dark:text-amber-300">
                <strong>Plano necessário:</strong> {config.requiredPlan}
              </p>
            </div>

            {/* Botão de upgrade */}
            <div className="flex flex-col gap-3">
              <Button
                className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-semibold py-3 text-lg shadow-lg"
                onClick={() => window.open(upgradeUrl, '_blank')}
              >
                <Crown className="h-5 w-5 mr-2" />
                Fazer Upgrade Agora
              </Button>
              
              <Button
                variant="outline"
                className="w-full border-slate-300 text-slate-600 hover:bg-slate-50 dark:border-slate-600 dark:text-slate-400 dark:hover:bg-slate-800"
                onClick={() => window.history.back()}
              >
                Voltar
              </Button>
            </div>

            {/* Garantia */}
            <div className="text-center text-xs text-slate-500 dark:text-slate-400 mt-4">
              <p>✨ Upgrade instantâneo • 30 dias de garantia • Cancele quando quiser</p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};