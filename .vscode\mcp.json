{"mcpServers": {"context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequentialthinking", "sequentialthinking"]}}}