-- Migration: Add create trial subscription trigger
-- Description: <PERSON><PERSON><PERSON> trigger para criar trial subscription automaticamente
-- Author: <PERSON> (ObrasAI Team)
-- Date: 2025-07-31

-- Função para criar trial subscription automaticamente
CREATE OR REPLACE FUNCTION public.create_trial_subscription_trigger()
<PERSON><PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  trial_start timestamptz;
  trial_end timestamptz;
BEGIN
  -- Definir período de trial de 7 dias
  trial_start := now();
  trial_end := now() + interval '7 days';
  
  -- Criar assinatura trial
  INSERT INTO subscriptions (
    user_id,
    plan_type,
    status,
    current_period_start,
    current_period_end,
    stripe_subscription_id,
    stripe_customer_id
  ) VALUES (
    NEW.id,
    'free',
    'trialing',
    trial_start,
    trial_end,
    null,
    null
  ) ON CONFLICT (user_id) DO NOTHING;
  
  -- Criar entrada de tracking de AI trial usage
  INSERT INTO ai_trial_usage (
    user_id,
    trial_start_date,
    trial_end_date,
    total_budget_requests
  ) VALUES (
    NEW.id,
    trial_start,
    trial_end,
    0
  ) ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Em caso de erro, logar mas não falhar a criação do usuário
    RAISE WARNING 'Erro ao criar trial subscription para usuário %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$;

-- Criar trigger para executar após criação do usuário
DROP TRIGGER IF EXISTS create_trial_subscription_on_signup ON auth.users;
CREATE TRIGGER create_trial_subscription_on_signup
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION create_trial_subscription_trigger();

-- Comentário explicativo
COMMENT ON FUNCTION public.create_trial_subscription_trigger() IS 'Cria trial subscription de 7 dias automaticamente para novos usuários';