import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { <PERSON>, EyeOff, Loader2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { z } from "zod";

import loginBg from "@/assets/images/footer.jpg";
import logoDarkHorizon from "@/assets/logo/logo_dark_horizon.png";
import { GoogleAuthButton } from "@/components/auth/GoogleAuthButton";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/auth";
import { t } from "@/lib/i18n";


// Esquema de validação do formulário
const loginSchema = z.object({
  email: z.string().email({
    message: t("messages.invalidEmail"),
  }),
  password: z.string().min(6, {
    message: t("messages.passwordTooShort"),
  }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

const Login = () => {
  const { login, user: _user, session, loading: _loading } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const redirectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasRedirectedRef = useRef(false);

  // Função de redirecionamento com timeout de segurança
  const performRedirect = useRef(() => {
    if (hasRedirectedRef.current) return;
    
    hasRedirectedRef.current = true;
    setIsRedirecting(true);
    navigate("/dashboard", { replace: true });
  });

  // Cleanup de timeout ao desmontar
  useEffect(() => {
    return () => {
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
      }
    };
  }, []);

  // Redirecionamento otimizado
  useEffect(() => {
    // Se já redirecionou, não fazer nada
    if (hasRedirectedRef.current || isRedirecting) return;

    // Condição principal: usuário autenticado (independente de loading)
    if (session?.user?.id) {
      console.log('Redirecionando para dashboard - usuário autenticado');
      
      // Timeout pequeno para garantir que a autenticação foi processada
      const timeoutId = setTimeout(() => {
        if (!hasRedirectedRef.current && !isRedirecting) {
          performRedirect.current();
        }
      }, 100); // 100ms delay mínimo
      
      redirectTimeoutRef.current = timeoutId;
      return () => clearTimeout(timeoutId);
    }
  }, [session?.user?.id, isRedirecting]); // ← Dependência mais específica

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    try {
      setIsLoading(true);
      hasRedirectedRef.current = false; // Reset para permitir novo redirecionamento
      
      const { error } = await login(data.email, data.password);
      
      if (error) {
        const errorMessage = (error instanceof Error) ? error.message : t("auth.loginError");
        toast.error(errorMessage);
        return;
      }
      
      toast.success(t("messages.loginSuccess"));
      
      // O redirecionamento será feito pelo useEffect quando session for atualizada
      
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : t("messages.generalError");
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Mostrar loading se está redirecionando
  if (isRedirecting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-800 mb-2">
            Redirecionando...
          </h2>
          <p className="text-gray-600">
            Aguarde enquanto carregamos seu dashboard
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen flex items-center justify-center p-4 overflow-hidden">
      {/* Imagem de fundo com overlay */}
      <img
        src={loginBg}
        alt="Background ObraVision"
        className="absolute inset-0 w-full h-full object-cover z-0"
        style={{ filter: "blur(0px) brightness(0.6)" }}
      />
      <div className="absolute inset-0 bg-black/70 z-10" />
      

      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="relative z-20 w-full max-w-md"
      >
        <Card className="border border-white/20 backdrop-blur-sm bg-white/5 shadow-2xl">
          <CardHeader className="space-y-1 text-center pb-8">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="flex justify-center mb-4"
            >
              <div className="h-16 w-full flex items-center justify-center">
                <img
                  src={logoDarkHorizon}
                  width={240}
                  height={64}
                  alt="Logo Obras.AI"
                  style={{ maxWidth: 240, maxHeight: 64, height: "auto", width: "auto", display: "block" }}
                  draggable={false}
                />
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <CardTitle className="text-3xl font-bold text-white text-center">
                Bem-vindo
              </CardTitle>
            </motion.div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <CardDescription className="text-white/90">
                Entre com suas credenciais para acessar o sistema
              </CardDescription>
            </motion.div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <GoogleAuthButton buttonText={t("auth.googleLogin")} />
            </motion.div>
            

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white font-medium">E-mail</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="<EMAIL>"
                            {...field}
                            disabled={isLoading}
                            className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-[#daa916] focus:ring-2 focus:ring-[#daa916]/20 transition-all duration-300"
                          />
                        </FormControl>
                        <FormMessage className="text-red-600" />
                      </FormItem>
                    )}
                  />
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 }}
                >
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white font-medium">Senha</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showPassword ? "text" : "password"}
                              placeholder="••••••••"
                              {...field}
                              disabled={isLoading}
                              className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-[#daa916] focus:ring-2 focus:ring-[#daa916]/20 pr-10 transition-all duration-300"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-600" />
                      </FormItem>
                    )}
                  />
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                >
                  <Button
                    type="submit"
                    className="w-full h-11 font-semibold bg-gray-700 border-gray-600 text-white hover:bg-gray-600 hover:border-[#daa916] shadow-lg transition-all duration-300 transform hover:scale-[1.02] border-0"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Entrando...
                      </>
                    ) : (
                      "Entrar no ObraVision"
                    )}
                  </Button>
                </motion.div>
              </form>
            </Form>
          </CardContent>
          
          <CardFooter className="flex flex-col space-y-3 pt-6">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.9 }}
              className="text-sm text-center"
            >
              <Link
                to="/forgot-password"
                className="text-white hover:text-white/80 transition-colors underline-offset-4 hover:underline"
              >
                Esqueceu sua senha?
              </Link>
            </motion.div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="text-sm text-center text-white/90"
            >
              Não tem uma conta?{" "}
              <Link
                to="/register"
                className="text-white hover:text-white/80 transition-colors font-medium underline-offset-4 hover:underline"
              >
                Criar conta
              </Link>
            </motion.div>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default Login;
