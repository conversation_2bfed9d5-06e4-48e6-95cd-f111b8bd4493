import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";

import OrcamentoRiskAlert from "@/components/orcamento/OrcamentoRiskAlert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useObrasCondominio } from "@/hooks/useObrasCondominio";
import { useOrcamentoRiskAlert, type DadosObra } from "@/hooks/useOrcamentoRiskAlert";
import { condominioSchema } from "@/lib/validators/condominioValidator";
import type { CreateCondominioData } from "@/types/condominio";

const CondominioForm: React.FC = () => {
  const { createCondominioRPC } = useObrasCondominio();

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateCondominioData>({
    resolver: zodResolver(condominioSchema),
    defaultValues: {
      obra_mae: {
        nome: "",
        endereco: "",
        cidade: "",
        estado: "",
        cep: "",
        orcamento: 0,
        area_total: 0,
      },
      unidades: [{ identificador_unidade: "101", nome: "Apartamento 101" }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "unidades",
  });

  // ===== ALERTA DE RISCO ORÇAMENTÁRIO =====
  
  // Observar mudanças nos campos relevantes
  const watchedFields = watch(['obra_mae.orcamento', 'obra_mae.area_total', 'obra_mae.estado']);
  const orcamento = watchedFields[0] || 0;
  const areaTotal = watchedFields[1] || 0;
  const estado = watchedFields[2] || '';

  // Preparar dados para análise de risco (condomínio vertical por padrão)
  const dadosObra: DadosObra = {
    tipo_obra: "R4_MULTIFAMILIAR",
    padrao_obra: "NORMAL",
    tipo_condominio: "VERTICAL", // Assumindo vertical por padrão
    area_total: areaTotal,
    area_construida: areaTotal,
    estado: estado,
    cidade: watch('obra_mae.cidade') || '',
    numero_unidades: fields.length,
  };

  // Hook de análise de risco
  const riskAlert = useOrcamentoRiskAlert(orcamento, dadosObra);

  // Função para aceitar sugestão da IA
  const handleAcceptSuggestion = (suggestedAmount: number) => {
    setValue('obra_mae.orcamento', suggestedAmount);
    toast.success("Orçamento atualizado com a sugestão da IA!");
  };

  const mutation = useMutation({
    mutationFn: createCondominioRPC,
    onSuccess: (data) => {
      toast.success(
        `Condomínio "${data.obra_mae.nome}" criado com sucesso! ${data.unidades.length} unidades adicionadas.`
      );
      // Resetar o formulário ou redirecionar
    },
    onError: (error) => {
      toast.error(`Erro ao criar condomínio: ${error.message}`);
    },
  });

  const onSubmit = (data: CreateCondominioData) => {
    // Converter para formato esperado pela RPC
    const apiData = {
      condominio_data: data.obra_mae,
      unidades_data: data.unidades,
    };

    mutation.mutate(apiData);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Dados do Condomínio</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="obra_mae.nome">Nome do Condomínio</Label>
            <Input id="obra_mae.nome" {...register("obra_mae.nome")} />
            {errors.obra_mae?.nome && (
              <p className="text-red-500">{errors.obra_mae.nome.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="obra_mae.orcamento">Orçamento Total</Label>
              <Input 
                id="obra_mae.orcamento" 
                type="number"
                step="0.01"
                {...register("obra_mae.orcamento", { valueAsNumber: true })} 
                placeholder="Ex: 5000000.00"
              />
              {errors.obra_mae?.orcamento && (
                <p className="text-red-500">{errors.obra_mae.orcamento.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="obra_mae.area_total">Área Total (m²)</Label>
              <Input 
                id="obra_mae.area_total" 
                type="number"
                step="0.01"
                {...register("obra_mae.area_total", { valueAsNumber: true })} 
                placeholder="Ex: 7600.00"
              />
              {errors.obra_mae?.area_total && (
                <p className="text-red-500">{errors.obra_mae.area_total.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="obra_mae.estado">Estado</Label>
            <Input id="obra_mae.estado" {...register("obra_mae.estado")} placeholder="Ex: GO" />
            {errors.obra_mae?.estado && (
              <p className="text-red-500">{errors.obra_mae.estado.message}</p>
            )}
          </div>

          {/* Alerta de Risco Orçamentário */}
          {(orcamento > 0 && areaTotal > 0) && (
            <div className="space-y-4">
              <Label className="text-sm font-medium text-muted-foreground">
                Validação Inteligente de Orçamento
              </Label>
              
              <OrcamentoRiskAlert
                riskAlert={riskAlert}
                onAcceptSuggestion={handleAcceptSuggestion}
                className="w-full"
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Unidades do Condomínio</CardTitle>
        </CardHeader>
        <CardContent>
          {fields.map((field, index) => (
            <div key={field.id} className="flex items-center space-x-4 mb-4">
              <Input
                {...register(`unidades.${index}.identificador_unidade`)}
                placeholder="Identificador"
              />
              <Input
                {...register(`unidades.${index}.nome`)}
                placeholder="Nome da Unidade"
              />
              <Button
                type="button"
                onClick={() => remove(index)}
                variant="destructive"
              >
                Remover
              </Button>
            </div>
          ))}
          <Button
            type="button"
            onClick={() => append({ identificador_unidade: "", nome: "" })}
          >
            Adicionar Unidade
          </Button>
        </CardContent>
      </Card>

      <CardFooter>
        <Button type="submit" disabled={mutation.isPending}>
          {mutation.isPending ? "Criando..." : "Criar Condomínio"}
        </Button>
      </CardFooter>
    </form>
  );
};

export default CondominioForm;
