-- <PERSON>ript para diagnosticar e corrigir problemas de signup
-- Execute no SQL Editor do Supabase Dashboard

-- 1. Veri<PERSON>r triggers ativos na tabela auth.users
SELECT 
  trigger_name,
  event_manipulation,
  action_timing,
  action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'users' 
AND event_object_schema = 'auth'
ORDER BY trigger_name;

-- 2. Verificar se as tabelas necessárias existem e têm as colunas corretas
\d public.subscriptions;
\d public.ai_trial_usage;
\d public.notification_preferences;

-- 3. Verificar constraints problemáticas na tabela subscriptions
SELECT 
  constraint_name,
  constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'subscriptions' 
AND table_schema = 'public'
ORDER BY constraint_name;

-- 4. Temporariamente desabilitar triggers problemáticos para teste
-- (Execute apenas se necessário)

-- Desabilitar trigger de trial subscription
-- ALTER TABLE auth.users DISABLE TRIGGER create_trial_subscription_on_signup;

-- Desabilitar trigger de notification preferences  
-- ALTER TABLE auth.users DISABLE TRIGGER create_notification_preferences_on_signup;

-- 5. Verificar se há dados conflitantes que podem estar causando constraint violations
SELECT 
  user_id, 
  COUNT(*) as count 
FROM public.subscriptions 
GROUP BY user_id 
HAVING COUNT(*) > 1;

-- 6. Verificar se há problemas na função create_trial_subscription_trigger
SELECT 
  proname as function_name,
  prosrc as function_body
FROM pg_proc 
WHERE proname = 'create_trial_subscription_trigger';

-- 7. Para reabilitar os triggers após correção:
-- ALTER TABLE auth.users ENABLE TRIGGER create_trial_subscription_on_signup;
-- ALTER TABLE auth.users ENABLE TRIGGER create_notification_preferences_on_signup;
