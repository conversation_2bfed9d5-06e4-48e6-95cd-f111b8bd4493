import { useQuery } from "@tanstack/react-query";

import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";

// Tipos de planos disponíveis
export type PlanType = 'free' | 'basic' | 'pro' | 'enterprise';

// Funcionalidades disponíveis por plano
export interface PlanFeatures {
  maxObras: number;
  maxUsuarios: number;
  aiFeatures: boolean;
  advancedAnalytics: boolean;
  apiAccess: boolean;
  prioritySupport: boolean;
  customTemplates: boolean;
  exportData: boolean;
}

// Configuração dos planos e suas funcionalidades
const PLAN_FEATURES: Record<PlanType, PlanFeatures> = {
  free: {
    maxObras: 3,
    maxUsuarios: 1,
    aiFeatures: false,
    advancedAnalytics: false,
    apiAccess: false,
    prioritySupport: false,
    customTemplates: false,
    exportData: false,
  },
  basic: {
    maxObras: 10,
    maxUsuarios: 3,
    aiFeatures: false,
    advancedAnalytics: false,
    apiAccess: false,
    prioritySupport: false,
    customTemplates: true,
    exportData: true,
  },
  pro: {
    maxObras: 50,
    maxUsuarios: 10,
    aiFeatures: true,
    advancedAnalytics: true,
    apiAccess: false,
    prioritySupport: true,
    customTemplates: true,
    exportData: true,
  },
  enterprise: {
    maxObras: -1, // Ilimitado
    maxUsuarios: -1, // Ilimitado
    aiFeatures: true,
    advancedAnalytics: true,
    apiAccess: true,
    prioritySupport: true,
    customTemplates: true,
    exportData: true,
  },
};

interface SubscriptionData {
  id: string;
  user_id: string;
  plan_type: PlanType;
  status: 'active' | 'canceled' | 'past_due' | 'trialing';
  current_period_start: string;
  current_period_end: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  created_at: string;
  updated_at: string;
}

// Modo de desenvolvimento - permite testar funcionalidades premium
const DEV_MODE = import.meta.env.DEV;
const DEV_PREMIUM_PLAN: PlanType = 'pro'; // Mude para 'free' se quiser testar restrições

export function useSubscription() {
  const { user } = useAuth();

  // Buscar dados da assinatura do usuário
  const { data: subscription, isLoading, error } = useQuery({
    queryKey: ["subscription", user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      // Em modo de desenvolvimento, usar plano premium para testes
      if (DEV_MODE) {
        console.log(`🧪 Modo DEV: Usando plano ${DEV_PREMIUM_PLAN}`);
        return {
          plan_type: DEV_PREMIUM_PLAN,
          status: 'active' as const,
        };
      }

      try {
        const { data, error } = await supabase
          .from("subscriptions")
          .select("*")
          .eq("user_id", user.id)
          .in("status", ["active", "trialing"]) // Incluir trials ativos
          .order("created_at", { ascending: false })
          .limit(1);

        if (error) {
          // Se houver erro na consulta, retorna plano gratuito
          console.log('📝 Erro na consulta de assinatura, usando plano gratuito:', error);
          return {
            plan_type: 'free' as PlanType,
            status: 'active' as const,
          };
        }

        // Se não há dados (array vazio), retorna plano gratuito
        if (!data || data.length === 0) {
          console.log('📝 Nenhuma assinatura ativa encontrada, usando plano gratuito');
          return {
            plan_type: 'free' as PlanType,
            status: 'active' as const,
          };
        }

        return data[0] as SubscriptionData;
      } catch (err) {
        // Em caso de erro (tabela não existe, etc), retorna plano gratuito
        console.warn('⚠️ Erro ao buscar assinatura, usando plano gratuito:', err);
        return {
          plan_type: 'free' as PlanType,
          status: 'active' as const,
        };
      }
    },
    enabled: !!user?.id,
    retry: false, // Não tentar novamente se falhar
    staleTime: 5 * 60 * 1000, // Cache por 5 minutos
  });

  // Determinar o plano atual
  const currentPlan: PlanType = subscription?.plan_type || 'free';
  
  // Verificar se o trial expirou
  const isTrialExpired =
    subscription?.status === "trialing" &&
    new Date(subscription.current_period_end) < new Date();

  // Obter funcionalidades do plano atual
  const planFeatures = PLAN_FEATURES[currentPlan];

  // Verificar se o usuário pode usar funcionalidades específicas
  const canUseFeature = {
    aiFeatures:
      planFeatures.aiFeatures ||
      (subscription?.status === "trialing" && !isTrialExpired),
    advancedAnalytics: planFeatures.advancedAnalytics,
    apiAccess: planFeatures.apiAccess,
    prioritySupport: planFeatures.prioritySupport,
    customTemplates: planFeatures.customTemplates,
    exportData: planFeatures.exportData,
  };

  // Verificar limites
  const limits = {
    maxObras: planFeatures.maxObras,
    maxUsuarios: planFeatures.maxUsuarios,
    hasUnlimitedObras: planFeatures.maxObras === -1,
    hasUnlimitedUsuarios: planFeatures.maxUsuarios === -1,
  };

  // URLs para upgrade
  const upgradeUrl =
    currentPlan === 'free'
      ? '/subscription?plan=pro'
      : '/subscription?plan=enterprise';

  // Informações sobre o trial
  const trialInfo =
    subscription?.status === "trialing"
      ? {
          isTrialing: true,
          isExpired: isTrialExpired,
          endDate: new Date(subscription.current_period_end),
          daysRemaining: Math.max(
            0,
            Math.ceil(
              (new Date(subscription.current_period_end).getTime() -
                Date.now()) /
                (1000 * 60 * 60 * 24)
            )
          ),
        }
      : {
          isTrialing: false,
          isExpired: false,
          endDate: null,
          daysRemaining: 0,
        };

  return {
    subscription,
    currentPlan,
    planFeatures,
    canUseFeature,
    limits,
    upgradeUrl,
    isLoading,
    error,
    isPremium: ['pro', 'enterprise'].includes(currentPlan),
    isEnterprise: currentPlan === 'enterprise',
    // Informações do trial
    trial: trialInfo,
    // Verificação de acesso
    hasAccess:
      !isTrialExpired || ['active'].includes(subscription?.status || ''),
    // Informações para debug
    isDev: DEV_MODE,
    devPlan: DEV_MODE ? DEV_PREMIUM_PLAN : null,
  };
}