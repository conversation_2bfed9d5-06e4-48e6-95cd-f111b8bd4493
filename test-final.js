import { createClient } from "@supabase/supabase-js";

const supabaseUrl = "https://anrphijuostbgbscxmzx.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc";

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testCompleteSignup() {
  console.log("🧪 Teste completo de signup com trial...");

  const testEmail = `test${Date.now()}@gmail.com`;
  console.log(`📧 Email de teste: ${testEmail}`);

  // 1. Fazer signup
  const { data, error } = await supabase.auth.signUp({
    email: testEmail,
    password: "TestPassword123!",
    options: {
      data: {
        first_name: "Test",
        last_name: "User",
      },
    },
  });

  if (error) {
    console.error("❌ Erro no signup:", error.message);
    return false;
  }

  console.log("✅ Signup bem-sucedido! ID:", data.user?.id);

  if (!data.user?.id) {
    console.error("❌ Usuário não foi criado");
    return false;
  }

  // 2. Aguardar um pouco para o trigger executar
  console.log("⏳ Aguardando trigger executar...");
  await new Promise((resolve) => setTimeout(resolve, 3000));

  // 3. Verificar se o trial foi criado
  const { data: subscription, error: subError } = await supabase
    .from("subscriptions")
    .select("*")
    .eq("user_id", data.user.id)
    .maybeSingle();

  if (subError) {
    console.log("⚠️ Trial não foi criado automaticamente:", subError.message);

    // Criar trial manualmente via Edge Function
    console.log("🔧 Tentando criar trial via Edge Function...");
    const { data: trialData, error: trialError } =
      await supabase.functions.invoke("create-trial-subscription", {
        body: { userId: data.user.id },
      });

    if (trialError) {
      console.error("❌ Erro ao criar trial via função:", trialError);
      return false;
    } else {
      console.log("✅ Trial criado via Edge Function:", trialData);
      return true;
    }
  } else {
    console.log("✅ Trial criado automaticamente:", subscription);
    return true;
  }
}

testCompleteSignup()
  .then((success) => {
    console.log(success ? "\n🎉 TUDO FUNCIONANDO!" : "\n❌ AINDA HÁ PROBLEMAS");
    process.exit(0);
  })
  .catch((err) => {
    console.error("ERRO:", err.message);
    process.exit(1);
  });
