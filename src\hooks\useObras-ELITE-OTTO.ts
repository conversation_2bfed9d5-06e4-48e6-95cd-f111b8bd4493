import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { useAuth } from "@/contexts/auth/hooks";
import { supabase } from "@/integrations/supabase/client";
import { queryKeys } from "@/lib/query-keys";
import type { Obra } from "@/types/api";

/**
 * Hook customizado para gerenciamento de obras - VERSÃO SIMPLIFICADA
 * Temporariamente simplificado para resolver problema de dependência circular
 */
export const useObras = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const tenantId = user?.profile?.tenant_id;
  const validTenantId =
    tenantId && typeof tenantId === "string" ? tenantId : null;

  // Query para buscar obras
  const {
    data: obras = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: queryKeys.obras(validTenantId || ""),
    queryFn: async () => {
      if (!validTenantId) return [];

      const { data, error } = await supabase
        .from("obras")
        .select("*")
        .eq("tenant_id", validTenantId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data as Obra[];
    },
    enabled: !!validTenantId,
  });

  // Mutation para atualizar obra
  const updateObra = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Obra> }) => {
      if (!validTenantId) throw new Error("Tenant ID não encontrado");

      const { data: result, error } = await supabase
        .from("obras")
        .update(data)
        .eq("id", id)
        .eq("tenant_id", validTenantId)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.obras(validTenantId || ""),
      });
      toast.success("Obra atualizada com sucesso!");
    },
    onError: (error) => {
      console.error("Erro ao atualizar obra:", error);
      toast.error("Erro ao atualizar obra");
    },
  });

  // Mutation para criar obra
  const createObra = useMutation({
    mutationFn: async (data: Partial<Obra>) => {
      if (!validTenantId) throw new Error("Tenant ID não encontrado");

      const { data: result, error } = await supabase
        .from("obras")
        .insert([{ ...data, tenant_id: validTenantId }])
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.obras(validTenantId || ""),
      });
      toast.success("Obra criada com sucesso!");
    },
    onError: (error) => {
      console.error("Erro ao criar obra:", error);
      toast.error("Erro ao criar obra");
    },
  });

  // Mutation para deletar obra
  const deleteObra = useMutation({
    mutationFn: async (id: string) => {
      if (!validTenantId) throw new Error("Tenant ID não encontrado");

      const { error } = await supabase
        .from("obras")
        .delete()
        .eq("id", id)
        .eq("tenant_id", validTenantId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.obras(validTenantId || ""),
      });
      toast.success("Obra excluída com sucesso!");
    },
    onError: (error) => {
      console.error("Erro ao excluir obra:", error);
      toast.error("Erro ao excluir obra");
    },
  });

  return {
    obras,
    isLoading,
    error,
    createObra,
    updateObra,
    deleteObra,
    tenantId: validTenantId,
    hasValidTenant: !!validTenantId,
  };
};
