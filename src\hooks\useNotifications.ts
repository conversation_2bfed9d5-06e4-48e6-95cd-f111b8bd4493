// ============================================================================
// HOOK: useNotifications - SISTEMA DE NOTIFICAÇÕES OBRASAI
// ============================================================================
// Hook personalizado para gerenciar notificações usando TanStack Query + Supabase
// Integração completa: Realtime + Cache + Estado otimizado
// ============================================================================

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect } from "react";
import { toast } from "sonner";

import { useAuth } from "@/contexts/auth/hooks";
import { supabase } from "@/integrations/supabase/client";

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

export type NotificationType =
  | "obra_prazo_vencendo"
  | "obra_orcamento_excedido"
  | "obra_status_alterado"
  | "novo_lead_capturado"
  | "contrato_assinado"
  | "contrato_vencendo"
  | "ia_analise_pronta"
  | "ia_orcamento_gerado"
  | "sinapi_atualizado"
  | "sistema_manutencao"
  | "pagamento_vencendo"
  | "pagamento_processado";

export type NotificationChannel = "in_app" | "email" | "push" | "sms";
export type NotificationPriority = "low" | "medium" | "high" | "urgent";

export interface Notification {
  id: string;
  user_id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  context_type?: string;
  context_id?: string;
  context_data?: Record<string, unknown>;
  channels: NotificationChannel[];
  is_read: boolean;
  read_at?: string;
  scheduled_for?: string;
  sent_at?: string;
  created_at: string;
  updated_at: string;
  expires_at?: string;
}

export interface NotificationPreferences {
  id: string;
  user_id: string;
  enabled: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone: string;
  preferences: Record<
    string,
    {
      enabled: boolean;
      channels: NotificationChannel[];
      [key: string]: unknown;
    }
  >;
  email_digest_frequency: "never" | "daily" | "weekly";
  email_digest_time: string;
  created_at: string;
  updated_at: string;
}

export interface CreateNotificationData {
  type: NotificationType;
  title: string;
  message: string;
  priority?: NotificationPriority;
  context_type?: string;
  context_id?: string;
  context_data?: Record<string, unknown>;
  channels?: NotificationChannel[];
  scheduled_for?: string;
}

// ============================================================================
// HOOK PRINCIPAL
// ============================================================================

export const useNotifications = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // ============================================================================
  // QUERIES - BUSCA DE DADOS
  // ============================================================================

  /**
   * Hook para buscar notificações do usuário
   */
  const {
    data: notifications = [],
    isLoading: isLoadingNotifications,
    error: notificationsError,
    refetch: refetchNotifications,
  } = useQuery({
    queryKey: ["notifications", user?.id],
    queryFn: async (): Promise<Notification[]> => {
      if (!user?.id) return [];

      const { data, error } = await supabase
        .from("notifications")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(50);

      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 2, // 2 minutos
    cacheTime: 1000 * 60 * 10, // 10 minutos
  });

  /**
   * Hook para buscar contagem de notificações não lidas
   */
  const {
    data: unreadCount = 0,
    isLoading: isLoadingUnreadCount,
    refetch: refetchUnreadCount,
  } = useQuery({
    queryKey: ["notifications-unread-count", user?.id],
    queryFn: async (): Promise<number> => {
      if (!user?.id) return 0;

      const { data, error } = await supabase.rpc(
        "get_unread_notifications_count",
        { user_uuid: user.id }
      );

      if (error) throw error;
      return data || 0;
    },
    enabled: !!user?.id,
    staleTime: 1000 * 30, // 30 segundos
    cacheTime: 1000 * 60 * 5, // 5 minutos
    refetchInterval: 1000 * 60, // Atualizar a cada minuto
  });

  /**
   * Hook para buscar preferências de notificação
   */
  const {
    data: preferences,
    isLoading: isLoadingPreferences,
    error: preferencesError,
    refetch: refetchPreferences,
  } = useQuery({
    queryKey: ["notification-preferences", user?.id],
    queryFn: async (): Promise<NotificationPreferences | null> => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from("notification_preferences")
        .select("*")
        .eq("user_id", user.id)
        .single();

      if (error && error.code !== "PGRST116") throw error;
      return data || null;
    },
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 5, // 5 minutos
    cacheTime: 1000 * 60 * 30, // 30 minutos
  });

  // ============================================================================
  // MUTATIONS - MODIFICAÇÃO DE DADOS
  // ============================================================================

  /**
   * Mutation para criar uma nova notificação
   */
  const createNotificationMutation = useMutation({
    mutationFn: async (data: CreateNotificationData) => {
      if (!user?.id) throw new Error("Usuário não autenticado");

      // Versão simplificada que bypassa a Edge Function temporariamente
      // Cria notificação diretamente na tabela
      const { data: result, error } = await supabase
        .from("notifications")
        .insert({
          user_id: user.id,
          type: data.type,
          title: data.title,
          message: data.message,
          priority: data.priority || "medium",
          context_type: data.context_type,
          context_id: data.context_id,
          context_data: data.context_data,
          channels: data.channels || ["in_app"],
          scheduled_for: data.scheduled_for,
          sent_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      // Invalidar cache para atualizar listas
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
      queryClient.invalidateQueries({
        queryKey: ["notifications-unread-count"],
      });
    },
    onError: (error) => {
      console.error("Erro ao criar notificação:", error);
      toast.error("Erro ao enviar notificação");
    },
  });

  /**
   * Mutation para marcar notificação como lida
   */
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await supabase.rpc("mark_notification_as_read", {
        notification_uuid: notificationId,
      });

      if (error) throw error;
    },
    onSuccess: (_, notificationId) => {
      // Atualizar cache localmente
      queryClient.setQueryData<Notification[]>(
        ["notifications", user?.id],
        (oldData) => {
          if (!oldData) return [];
          return oldData.map((notification) =>
            notification.id === notificationId
              ? {
                  ...notification,
                  is_read: true,
                  read_at: new Date().toISOString(),
                }
              : notification
          );
        }
      );

      // Atualizar contagem de não lidas
      queryClient.setQueryData<number>(
        ["notifications-unread-count", user?.id],
        (oldCount) => Math.max(0, (oldCount || 0) - 1)
      );
    },
    onError: (error) => {
      console.error("Erro ao marcar como lida:", error);
      toast.error("Erro ao atualizar notificação");
    },
  });

  /**
   * Mutation para marcar todas as notificações como lidas
   */
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id) throw new Error("Usuário não autenticado");

      const { error } = await supabase
        .from("notifications")
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq("user_id", user.id)
        .eq("is_read", false);

      if (error) throw error;
    },
    onSuccess: () => {
      // Invalidar cache para recarregar dados
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
      queryClient.setQueryData(["notifications-unread-count", user?.id], 0);
      toast.success("Todas as notificações foram marcadas como lidas");
    },
    onError: (error) => {
      console.error("Erro ao marcar todas como lidas:", error);
      toast.error("Erro ao atualizar notificações");
    },
  });

  /**
   * Mutation para atualizar preferências
   */
  const updatePreferencesMutation = useMutation({
    mutationFn: async (newPreferences: Partial<NotificationPreferences>) => {
      if (!user?.id) throw new Error("Usuário não autenticado");

      // Primeiro, tentar buscar preferências existentes
      const { data: existing } = await supabase
        .from("notification_preferences")
        .select("id")
        .eq("user_id", user.id)
        .single();

      let data, error;

      if (existing) {
        // Se existir, fazer UPDATE
        const updateResult = await supabase
          .from("notification_preferences")
          .update({
            ...newPreferences,
            updated_at: new Date().toISOString(),
          })
          .eq("user_id", user.id)
          .select()
          .single();

        data = updateResult.data;
        error = updateResult.error;
      } else {
        // Se não existir, fazer INSERT
        const insertResult = await supabase
          .from("notification_preferences")
          .insert({
            user_id: user.id,
            ...newPreferences,
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        data = insertResult.data;
        error = insertResult.error;
      }

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Atualizar cache
      queryClient.setQueryData(["notification-preferences", user?.id], data);
      toast.success("Preferências atualizadas com sucesso");
    },
    onError: (error) => {
      console.error("Erro ao atualizar preferências:", error);
      toast.error("Erro ao salvar preferências");
    },
  });

  /**
   * Mutation para deletar notificação
   */
  const deleteNotificationMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await supabase
        .from("notifications")
        .delete()
        .eq("id", notificationId)
        .eq("user_id", user?.id);

      if (error) throw error;
    },
    onSuccess: (_, notificationId) => {
      // Remover do cache local
      queryClient.setQueryData<Notification[]>(
        ["notifications", user?.id],
        (oldData) => {
          if (!oldData) return [];
          return oldData.filter(
            (notification) => notification.id !== notificationId
          );
        }
      );

      // Atualizar contagem se era não lida
      const notification = notifications.find((n) => n.id === notificationId);
      if (notification && !notification.is_read) {
        queryClient.setQueryData<number>(
          ["notifications-unread-count", user?.id],
          (oldCount) => Math.max(0, (oldCount || 0) - 1)
        );
      }

      toast.success("Notificação removida");
    },
    onError: (error) => {
      console.error("Erro ao deletar notificação:", error);
      toast.error("Erro ao remover notificação");
    },
  });

  // ============================================================================
  // REALTIME SUBSCRIPTION
  // ============================================================================

  useEffect(() => {
    if (!user?.id) return;

    // Canal para notificações em tempo real
    const channel = supabase.channel(`user:${user.id}`);

    // Escutar novas notificações
    channel
      .on("broadcast", { event: "new_notification" }, (payload) => {
        const newNotification = payload.payload;

        // Adicionar ao cache local
        queryClient.setQueryData<Notification[]>(
          ["notifications", user.id],
          (oldData) => {
            if (!oldData) return [newNotification];
            return [newNotification, ...oldData];
          }
        );

        // Atualizar contagem de não lidas
        queryClient.setQueryData<number>(
          ["notifications-unread-count", user.id],
          (oldCount) => (oldCount || 0) + 1
        );

        // Mostrar toast baseado na prioridade
        const toastOptions = {
          duration: newNotification.priority === "urgent" ? 10000 : 5000,
        };

        switch (newNotification.priority) {
          case "urgent":
            toast.error(newNotification.title, toastOptions);
            break;
          case "high":
            toast.warning(newNotification.title, toastOptions);
            break;
          case "medium":
            toast.info(newNotification.title, toastOptions);
            break;
          default:
            toast(newNotification.title, toastOptions);
        }
      })
      .subscribe();

    // Cleanup
    return () => {
      supabase.removeChannel(channel);
    };
  }, [user?.id, queryClient]);

  // ============================================================================
  // FUNÇÕES AUXILIARES
  // ============================================================================

  /**
   * Filtra notificações por tipo
   */
  const getNotificationsByType = useCallback(
    (type: NotificationType) => {
      return notifications.filter((notification) => notification.type === type);
    },
    [notifications]
  );

  /**
   * Filtra notificações não lidas
   */
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter((notification) => !notification.is_read);
  }, [notifications]);

  /**
   * Filtra notificações por prioridade
   */
  const getNotificationsByPriority = useCallback(
    (priority: NotificationPriority) => {
      return notifications.filter(
        (notification) => notification.priority === priority
      );
    },
    [notifications]
  );

  /**
   * Função para criar notificação de teste
   */
  const sendTestNotification = useCallback(async () => {
    await createNotificationMutation.mutateAsync({
      type: "sistema_manutencao",
      title: "Notificação de Teste",
      message: "Esta é uma notificação de teste do sistema ObrasAI.",
      priority: "medium",
      channels: ["in_app"],
    });
  }, [createNotificationMutation]);

  // ============================================================================
  // RETURN DO HOOK
  // ============================================================================

  return {
    // Dados
    notifications,
    unreadCount,
    preferences,

    // Estados de loading
    isLoadingNotifications,
    isLoadingUnreadCount,
    isLoadingPreferences,

    // Erros
    notificationsError,
    preferencesError,

    // Funções de ação
    createNotification: createNotificationMutation.mutateAsync,
    markAsRead: markAsReadMutation.mutateAsync,
    markAllAsRead: markAllAsReadMutation.mutateAsync,
    updatePreferences: updatePreferencesMutation.mutateAsync,
    deleteNotification: deleteNotificationMutation.mutateAsync,

    // Estados das mutations
    isCreatingNotification: createNotificationMutation.isPending,
    isMarkingAsRead: markAsReadMutation.isPending,
    isMarkingAllAsRead: markAllAsReadMutation.isPending,
    isUpdatingPreferences: updatePreferencesMutation.isPending,
    isDeletingNotification: deleteNotificationMutation.isPending,

    // Funções auxiliares
    getNotificationsByType,
    getUnreadNotifications,
    getNotificationsByPriority,
    sendTestNotification,

    // Funções de refetch
    refetchNotifications,
    refetchUnreadCount,
    refetchPreferences,
  };
};

// ============================================================================
// HOOK PARA CONTEXTO ESPECÍFICO
// ============================================================================

/**
 * Hook para notificações específicas de obras
 */
export const useObraNotifications = (obraId?: string) => {
  const { notifications, createNotification } = useNotifications();

  const obraNotifications = notifications.filter(
    (notification) =>
      notification.context_type === "obra" &&
      (!obraId || notification.context_id === obraId)
  );

  const sendObraNotification = useCallback(
    async (
      type: Extract<
        NotificationType,
        | "obra_prazo_vencendo"
        | "obra_orcamento_excedido"
        | "obra_status_alterado"
      >,
      title: string,
      message: string,
      obraData: Record<string, unknown>
    ) => {
      // Verificar se já existe uma notificação similar nas últimas 24 horas
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const existingNotification = notifications.find(
        (notification) =>
          notification.type === type &&
          notification.context_type === "obra" &&
          notification.context_id === obraId &&
          notification.title === title &&
          new Date(notification.created_at) > oneDayAgo
      );

      // Se já existe uma notificação similar recente, não criar outra
      if (existingNotification) {
        console.log(
          `Notificação duplicada evitada: ${type} para obra ${obraId}`
        );
        return;
      }

      await createNotification({
        type,
        title,
        message,
        context_type: "obra",
        context_id: obraId,
        context_data: obraData,
        priority: type === "obra_orcamento_excedido" ? "high" : "medium",
      });
    },
    [createNotification, obraId, notifications]
  );

  return {
    obraNotifications,
    sendObraNotification,
  };
};

/**
 * Hook para notificações de leads
 */
export const useLeadNotifications = () => {
  const { notifications, createNotification } = useNotifications();

  const leadNotifications = notifications.filter(
    (notification) => notification.context_type === "lead"
  );

  const sendLeadNotification = useCallback(
    async (leadData: Record<string, unknown>) => {
      await createNotification({
        type: "novo_lead_capturado",
        title: "Novo Lead Capturado",
        message: `Novo contato interessado: ${leadData.nome}`,
        context_type: "lead",
        context_id: leadData.id,
        context_data: leadData,
        priority: "high",
        channels: ["in_app", "email"],
      });
    },
    [createNotification]
  );

  return {
    leadNotifications,
    sendLeadNotification,
  };
};
