import { createClient } from "@supabase/supabase-js";

const supabaseUrl = "https://anrphijuostbgbscxmzx.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc";

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testSignup() {
  console.log("Testando signup...");

  const testEmail = `test${Date.now()}@gmail.com`;
  const { data, error } = await supabase.auth.signUp({
    email: testEmail,
    password: "TestPassword123!",
    options: {
      data: {
        first_name: "Test",
        last_name: "User",
      },
    },
  });

  if (error) {
    console.error("ERRO:", error.message);
    return false;
  } else {
    console.log("SUCESSO! ID:", data.user?.id);
    return true;
  }
}

testSignup()
  .then((success) => {
    console.log(success ? "✅ FUNCIONOU!" : "❌ AINDA COM ERRO");
    process.exit(0);
  })
  .catch((err) => {
    console.error("ERRO:", err.message);
    process.exit(1);
  });
