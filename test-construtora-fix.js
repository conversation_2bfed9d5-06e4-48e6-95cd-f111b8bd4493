const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Variáveis de ambiente não encontradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConstrutoraCreation() {
  console.log('🔍 Testando criação de construtora...\n');

  // 1. Fazer login
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'sua_senha_aqui' // Você precisa colocar sua senha
  });

  if (authError) {
    console.error('❌ Erro ao fazer login:', authError.message);
    return;
  }

  console.log('✅ Login realizado com sucesso');
  console.log('👤 Usuário:', authData.user.email);
  
  // 2. Buscar tenant_id do perfil
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('tenant_id')
    .eq('id', authData.user.id)
    .single();

  if (profileError) {
    console.error('❌ Erro ao buscar perfil:', profileError.message);
    return;
  }

  console.log('🏢 Tenant ID:', profile.tenant_id);

  // 3. Tentar criar uma construtora de teste
  const construtoraData = {
    documento: '12345678000190',
    nome_razao_social: 'Construtora Teste RLS',
    email: '<EMAIL>',
    telefone: '11999999999',
    tipo: 'pj',
    tenant_id: profile.tenant_id,
    endereco: 'Rua Teste',
    numero: '123',
    bairro: 'Centro',
    cidade: 'São Paulo',
    estado: 'SP',
    cep: '01000000'
  };

  console.log('\n📝 Tentando criar construtora...');
  
  const { data: construtora, error: createError } = await supabase
    .from('construtoras')
    .insert([construtoraData])
    .select()
    .single();

  if (createError) {
    console.error('❌ Erro ao criar construtora:', createError.message);
    console.error('Detalhes:', createError);
  } else {
    console.log('✅ Construtora criada com sucesso!');
    console.log('ID:', construtora.id);
    console.log('Nome:', construtora.nome_razao_social);
  }

  // 4. Verificar se consegue listar construtoras
  console.log('\n📋 Listando construtoras do tenant...');
  
  const { data: construtoras, error: listError } = await supabase
    .from('construtoras')
    .select('id, nome_razao_social, tipo')
    .eq('tenant_id', profile.tenant_id);

  if (listError) {
    console.error('❌ Erro ao listar construtoras:', listError.message);
  } else {
    console.log(`✅ Encontradas ${construtoras.length} construtoras:`);
    construtoras.forEach(c => {
      console.log(`  - ${c.nome_razao_social} (${c.tipo})`);
    });
  }

  // Fazer logout
  await supabase.auth.signOut();
  console.log('\n👋 Logout realizado');
}

// Executar teste
testConstrutoraCreation().catch(console.error);