-- CORREÇÃO SIMPLES E SEGURA PARA ERRO DE SIGNUP
-- Execute cada comando separadamente no SQL Editor

-- PASSO 1: Verificar triggers existentes
SELECT 
  trigger_name,
  event_manipulation,
  action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'users' 
AND event_object_schema = 'auth'
ORDER BY trigger_name;

-- PASSO 2: Desabilitar trigger problemático (execute apenas se existir)
-- ALTER TABLE auth.users DISABLE TRIGGER create_trial_subscription_on_signup;

-- PASSO 3: Testar signup (use o script test-signup-fix.js)

-- PASSO 4: Se o signup funcionar, corrigir a função do trigger
CREATE OR REPLACE FUNCTION public.create_trial_subscription_trigger()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Tentar criar subscription com tratamento de erro
  BEGIN
    INSERT INTO subscriptions (
      user_id,
      plan_type,
      status,
      current_period_start,
      current_period_end,
      stripe_subscription_id,
      stripe_customer_id
    ) VALUES (
      NEW.id,
      'free',
      'trialing',
      now(),
      now() + interval '7 days',
      null,
      null
    );
  EXCEPTION
    WHEN unique_violation THEN
      -- Usuário já tem subscription, ignorar
      NULL;
    WHEN OTHERS THEN
      -- Log erro mas não falha
      RAISE WARNING 'Erro ao criar subscription: %', SQLERRM;
  END;
  
  -- Tentar criar ai_trial_usage com tratamento de erro
  BEGIN
    INSERT INTO ai_trial_usage (
      user_id,
      trial_start_date,
      trial_end_date,
      total_budget_requests
    ) VALUES (
      NEW.id,
      now(),
      now() + interval '7 days',
      0
    );
  EXCEPTION
    WHEN unique_violation THEN
      -- Usuário já tem trial usage, ignorar
      NULL;
    WHEN OTHERS THEN
      -- Log erro mas não falha
      RAISE WARNING 'Erro ao criar ai_trial_usage: %', SQLERRM;
  END;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Garantir que nunca falhe a criação do usuário
    RAISE WARNING 'Erro geral no trigger: %', SQLERRM;
    RETURN NEW;
END;
$$;

-- PASSO 5: Corrigir constraint problemática
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_user_id_status_key;

-- PASSO 6: Criar constraint correta
DROP INDEX IF EXISTS idx_subscriptions_user_active;
CREATE UNIQUE INDEX idx_subscriptions_user_active
ON subscriptions (user_id) 
WHERE status IN ('active', 'trialing');

-- PASSO 7: Reabilitar trigger (execute apenas se foi desabilitado)
-- ALTER TABLE auth.users ENABLE TRIGGER create_trial_subscription_on_signup;
