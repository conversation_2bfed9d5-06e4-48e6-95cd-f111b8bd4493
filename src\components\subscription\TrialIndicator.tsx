import { motion } from "framer-motion";
import { Calendar, Clock, Crown, Zap } from "lucide-react";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAIQuota } from "@/hooks/useAIQuota";
import { useSubscription } from "@/hooks/useSubscription";

export const TrialIndicator = () => {
  const { trial, subscription } = useSubscription();
  const navigate = useNavigate();
  const { quotas } = useAIQuota();

  const trialInfo = useMemo(() => {
    if (!trial.isTrialing || trial.isExpired) return null;

    const now = new Date();
    const endDate = trial.endDate;
    const startDate = subscription
      ? new Date(subscription.current_period_start)
      : new Date();

    const totalDays = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const remainingMs = endDate.getTime() - now.getTime();
    const remainingDays = Math.max(
      0,
      Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
    );
    const remainingHours = Math.max(
      0,
      Math.ceil(remainingMs / (1000 * 60 * 60))
    );

    const progress = ((totalDays - remainingDays) / totalDays) * 100;
    const isExpiringSoon = remainingDays <= 2;
    const isLastDay = remainingDays <= 1;

    return {
      totalDays,
      remainingDays,
      remainingHours,
      progress,
      isExpiringSoon,
      isLastDay,
      endDate: endDate.toLocaleDateString("pt-BR"),
    };
  }, [trial, subscription]);

  if (!trialInfo) return null;

  const handleUpgrade = () => {
    navigate("/subscription?plan=pro&source=trial");
  };

  const getStatusColor = () => {
    if (trialInfo.isLastDay) return "bg-red-500";
    if (trialInfo.isExpiringSoon) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getStatusText = () => {
    if (trialInfo.isLastDay && trialInfo.remainingHours <= 24) {
      return `${trialInfo.remainingHours}h restantes`;
    }
    return `${trialInfo.remainingDays} dias restantes`;
  };

  // Formatação das quotas específicas do trial
  const quotaInfo = [
    {
      label: "Chat IA",
      current: quotas.chat.current,
      limit: quotas.chat.limit,
      canUse: quotas.chat.canUse,
      suffix: "hoje",
    },
    {
      label: "Orçamentos",
      current: quotas.budget.current,
      limit: quotas.budget.limit,
      canUse: quotas.budget.canUse,
      suffix: "no trial",
      isTotal: true,
    },
    {
      label: "Contratos",
      current: 0,
      limit: 0,
      canUse: false,
      blocked: true,
    },
    {
      label: "SINAPI",
      current: quotas.sinapi.current,
      limit: quotas.sinapi.limit,
      canUse: quotas.sinapi.canUse,
      suffix: "hoje",
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`fixed top-16 right-4 z-50 max-w-sm ${
        trialInfo.isExpiringSoon ? "animate-pulse" : ""
      }`}
    >
      <Card
        className={`border-2 ${
          trialInfo.isLastDay
            ? "border-red-500"
            : trialInfo.isExpiringSoon
            ? "border-yellow-500"
            : "border-green-500"
        } shadow-lg bg-white dark:bg-gray-900`}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <div
              className={`w-3 h-3 rounded-full ${getStatusColor()} animate-pulse`}
            />
            <Badge variant="outline" className="text-xs">
              <Crown className="w-3 h-3 mr-1" />
              Trial Gratuito
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4" />
                <span className="font-medium">{getStatusText()}</span>
              </div>
              <div className="text-xs text-gray-500">
                até {trialInfo.endDate}
              </div>
            </div>

            <Progress value={trialInfo.progress} className="h-2" />

            {/* Quotas do Trial */}
            <div className="space-y-2 text-xs">
              <div className="font-medium text-gray-700 dark:text-gray-300">
                Quotas do Trial:
              </div>
              {quotaInfo.map((quota, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">
                    {quota.label}:
                  </span>
                  {quota.blocked ? (
                    <span className="text-gray-500 dark:text-gray-500">
                      Não disponível 🔒
                    </span>
                  ) : (
                    <span
                      className={`font-medium ${
                        quota.canUse
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {quota.current}/{quota.limit} {quota.suffix}{" "}
                      {quota.canUse ? "✅" : "❌"}
                    </span>
                  )}
                </div>
              ))}
            </div>

            <div className="text-xs text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-1 mb-1">
                <Zap className="w-3 h-3" />
                Acesso limitado às funcionalidades de IA
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {trialInfo.totalDays} dias de teste gratuito
              </div>
            </div>

            {trialInfo.isExpiringSoon && (
              <motion.div
                initial={{ scale: 0.95 }}
                animate={{ scale: 1 }}
                transition={{
                  repeat: Infinity,
                  repeatType: "reverse",
                  duration: 1.5,
                }}
              >
                <Button
                  onClick={handleUpgrade}
                  className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold"
                  size="sm"
                >
                  <Crown className="w-4 h-4 mr-2" />
                  Fazer Upgrade Agora
                </Button>
              </motion.div>
            )}

            {!trialInfo.isExpiringSoon && (
              <Button
                onClick={handleUpgrade}
                variant="outline"
                className="w-full"
                size="sm"
              >
                Ver Planos Premium
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
