import { useQuery } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { motion } from "framer-motion";
import {
  AlertTriangle,
  ArrowLeft,
  BarChart3,
  Building,
  Calculator,
  Calendar,
  Check,
  Clock,
  DollarSign,
  Home,
  Loader2,
  MapPin,
  MessageSquare,
  Pencil,
  Plus,
  Receipt,
  Trash2,
  TrendingUp,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import InsightsObra from "@/components/ai/InsightsObra";
import InterfaceChat from "@/components/ai/InterfaceChat";
import VendaLucroTab from "@/components/dashboard/obras/VendaLucroTab";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import { CondominioDashboard } from "@/components/obras/CondominioDashboard";
import { ListaUnidades } from "@/components/obras/ListaUnidades";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import type { Despesa } from "@/hooks/useDespesas";
import { useDespesas } from "@/hooks/useDespesas";
import { useObraNotifications } from "@/hooks/useNotifications";
import { useObrasCondominio } from "@/hooks/useObrasCondominio";
import { useTenantValidation } from "@/hooks/useTenantValidation";
import { supabase } from "@/integrations/supabase/client";
import { formatCurrencyBR, formatDateBR } from "@/lib/i18n";
import { cn } from "@/lib/utils";
import {
  STATUS_ORCAMENTO_CORES,
  STATUS_ORCAMENTO_LABELS,
  TIPO_OBRA_LABELS,
} from "@/lib/validations/orcamento";
import { obrasApi } from "@/services/api";
import {
  orcamentosParametricosApi,
  orcamentoUtils,
} from "@/services/orcamentoApi";

// Função para buscar despesas com rateio para unidades de condomínio
const buscarDespesasComRateio = async (
  unidadeId: string,
  masterObraId: string,
  orcamentoUnidade: number
) => {
  try {
    // 1. Buscar despesas específicas da unidade no master
    const { data: despesasUnidade, error: errorUnidade } = await supabase
      .from("despesas")
      .select(
        `
        *,
        unidade_obra:obras!unidade_especifica_id(identificador_unidade, nome)
      `
      )
      .eq("obra_id", masterObraId)
      .eq("tipo_despesa", "ESPECIFICA")
      .eq("unidade_especifica_id", unidadeId)
      .order("data_despesa", { ascending: false });

    if (errorUnidade) {
      console.error("Erro ao buscar despesas da unidade:", errorUnidade);
    }

    // 2. Buscar despesas comuns do condomínio master (para rateio)
    const { data: despesasMaster, error: errorMaster } = await supabase
      .from("despesas")
      .select("*")
      .eq("obra_id", masterObraId)
      .eq("tipo_despesa", "COMUM")
      .eq("rateio_automatico", true)
      .order("data_despesa", { ascending: false });

    if (errorMaster) {
      console.error("Erro ao buscar despesas do master:", errorMaster);
      return despesasUnidade || [];
    }

    // 3. Buscar orçamento total do master e de todas as unidades para calcular proporção
    const { data: masterData } = await supabase
      .from("obras")
      .select("orcamento")
      .eq("id", masterObraId)
      .single();

    const { data: todasUnidades } = await supabase
      .from("obras")
      .select("orcamento")
      .eq("parent_obra_id", masterObraId)
      .eq("tipo_projeto", "UNIDADE_CONDOMINIO");

    const orcamentoTotalUnidades =
      todasUnidades?.reduce((sum, u) => sum + (u.orcamento || 0), 0) || 0;
    const orcamentoMaster = masterData?.orcamento || 0;

    // 4. Calcular proporção desta unidade
    const proporcaoUnidade =
      orcamentoTotalUnidades > 0
        ? orcamentoUnidade / orcamentoTotalUnidades
        : 1 / (todasUnidades?.length || 1);

    // 5. Criar despesas "virtuais" rateadas do master (apenas despesas comuns)
    const despesasRateadas =
      despesasMaster?.map((despesa) => ({
        ...despesa,
        id: `${despesa.id}_rateado`, // ID único para evitar conflitos
        custo: Math.round((despesa.custo || 0) * proporcaoUnidade * 100) / 100,
        valor_unitario:
          Math.round((despesa.valor_unitario || 0) * proporcaoUnidade * 100) /
          100,
        descricao: `[RATEADO] ${despesa.descricao}`,
        quantidade:
          Math.round((despesa.quantidade || 0) * proporcaoUnidade * 100) / 100,
        obra_id: unidadeId, // Associar à unidade para exibição
        tipo_despesa: "COMUM",
        rateio_automatico: true,
        _isRateado: true, // Flag para identificar despesas rateadas
        _proporcao: proporcaoUnidade,
        _custoOriginal: despesa.custo,
      })) || [];

    // 6. Combinar despesas diretas + rateadas
    const todasDespesas = [...(despesasUnidade || []), ...despesasRateadas];

    return todasDespesas.sort(
      (a, b) =>
        new Date(b.data_despesa).getTime() - new Date(a.data_despesa).getTime()
    );
  } catch (error) {
    console.error("Erro ao buscar despesas com rateio:", error);
    return [];
  }
};

const ObraDetalhe = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("detalhes");
  const { deleteDespesa } = useDespesas();
  const [despesaToDelete, setDespesaToDelete] = useState<string | null>(null);

  // 🛡️ SEGURANÇA: Hook para validação de tenant
  const { validTenantId } = useTenantValidation();

  // Hook para notificações de obra
  const { sendObraNotification } = useObraNotifications(id);

  // Hook para funcionalidades específicas de condomínio
  const { getUnidadesCondominio } = useObrasCondominio();

  const {
    data: obra,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["obra", id],
    queryFn: async () => {
      if (!validTenantId) {
        throw new Error("Tenant não autenticado");
      }
      const result = await obrasApi.getById(id!, validTenantId); // ✅ PASSANDO TENANT_ID
      return result;
    },
    enabled: !!id && !!validTenantId, // ✅ HABILITADO APENAS SE TENANT VÁLIDO
    // ✅ Garantir que sempre busque dados frescos quando necessário
    staleTime: 30 * 1000, // 30 segundos
    cacheTime: 5 * 60 * 1000, // 5 minutos
  });

  // Query para orçamentos relacionados
  const { data: orcamentosRelacionados } = useQuery({
    queryKey: ["orcamentos-obra", id],
    queryFn: () => orcamentosParametricosApi.getByObra(id!),
    enabled: !!id,
  });

  // Query para despesas da obra (dados reais + rateio para unidades)
  const { data: despesasObra, isLoading: isLoadingDespesas } = useQuery({
    queryKey: ["despesas-obra", id, obra?.tipo_projeto, obra?.parent_obra_id],
    queryFn: async () => {
      if (!id) throw new Error("ID da obra é obrigatório");

      // Se for uma unidade de condomínio, buscar despesas rateadas do master
      if (obra?.tipo_projeto === "UNIDADE_CONDOMINIO" && obra?.parent_obra_id) {
        return await buscarDespesasComRateio(
          id,
          obra.parent_obra_id,
          obra.orcamento || 0
        );
      }

      // Para obras normais ou condomínio master, buscar despesas consolidadas
      const { data, error } = await supabase
        .from("despesas")
        .select(
          `
          id,
          descricao,
          custo,
          data_despesa,
          pago,
          categoria,
          quantidade,
          valor_unitario,
          etapa,
          insumo,
          data_pagamento,
          tipo_despesa,
          unidade_especifica_id,
          rateio_automatico,
          observacoes_unidade,
          unidade_obra:obras!unidade_especifica_id(identificador_unidade, nome)
        `
        )
        .eq("obra_id", id)
        .order("data_despesa", { ascending: false });

      if (error) {
        console.error("Erro ao buscar despesas:", error);
        throw error;
      }

      return data || [];
    },
    enabled: !!id && !!obra,
    onSuccess: () => {
      refetch(); // Refetch obra data to update metrics
    },
  });

  // Query para unidades do condomínio (se for um condomínio)
  const { data: unidadesCondominio, isLoading: isLoadingUnidades } = useQuery({
    queryKey: ["unidades-condominio", id],
    queryFn: () => getUnidadesCondominio(id!),
    enabled: !!id && obra?.tipo_projeto === "CONDOMINIO_MASTER",
  });

  // Query para dados do condomínio pai (se for uma unidade)
  const { data: condominioPai } = useQuery({
    queryKey: ["condominio-pai", obra?.parent_obra_id],
    queryFn: async () => {
      if (!validTenantId || !obra?.parent_obra_id) return null;
      const result = await obrasApi.getById(obra.parent_obra_id, validTenantId);
      return result;
    },
    enabled:
      !!obra?.parent_obra_id &&
      obra?.tipo_projeto === "UNIDADE_CONDOMINIO" &&
      !!validTenantId,
  });

  // ====================================
  // 🧮 CÁLCULOS DE MÉTRICAS REAIS
  // ====================================

  /**
   * Calcula progresso real da obra baseado em cronograma
   */
  const calcularProgressoObra = () => {
    if (!obra?.data_inicio || !obra?.data_prevista_termino) {
      return 0; // ✅ Retorna 0% em vez de undefined
    }

    try {
      const dataInicio = new Date(obra.data_inicio);
      const dataFim = new Date(obra.data_prevista_termino);
      const hoje = new Date();

      // Se ainda não começou, progresso = 0%
      if (hoje < dataInicio) {
        return 0;
      }

      // Se já terminou, progresso = 100%
      if (hoje > dataFim) {
        return 100;
      }

      const duracaoTotal = dataFim.getTime() - dataInicio.getTime();
      const tempoDecorrido = hoje.getTime() - dataInicio.getTime();

      const progresso = Math.max(
        0,
        Math.min(100, (tempoDecorrido / duracaoTotal) * 100)
      );

      return Math.round(progresso);
    } catch (_error) {
      console.error("❌ Erro ao calcular progresso:", error);
      return 0;
    }
  };

  /**
   * Calcula total gasto real baseado nas despesas
   */
  const calcularTotalGastos = () => {
    if (!despesasObra) return 0;

    return despesasObra.reduce((total, despesa) => {
      // Usar campo 'custo' que é valor_unitario * quantidade
      return total + (despesa.custo || 0);
    }, 0);
  };

  /**
   * Calcula capital disponível para construção (investimento - terreno)
   */
  const calcularCapitalConstrucao = () => {
    const investimentoTotal = obra?.orcamento || 0;
    const custoTerreno = obra?.custo_terreno || 0;
    return Math.max(0, investimentoTotal - custoTerreno);
  };

  /**
   * Calcula percentual gasto em relação ao capital disponível para construção
   */
  const calcularPercentualGasto = () => {
    const capitalConstrucao = calcularCapitalConstrucao();
    if (!capitalConstrucao || capitalConstrucao === 0) return null;

    const percentual = (totalGastos / capitalConstrucao) * 100;
    return Math.round(percentual * 100) / 100; // 2 casas decimais
  };

  /**
   * Calcula dias restantes até conclusão
   */
  const calcularDiasRestantes = () => {
    if (!obra?.data_inicio || !obra?.data_prevista_termino) {
      return null;
    }

    try {
      const dataInicio = new Date(obra.data_inicio);
      const dataFim = new Date(obra.data_prevista_termino);
      const hoje = new Date();

      // Calcular duração total da obra
      const duracaoTotal = Math.ceil(
        (dataFim.getTime() - dataInicio.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Se ainda não começou, retorna duração total
      if (hoje < dataInicio) {
        return duracaoTotal;
      }

      // Calcular dias restantes até o fim
      const diffTime = dataFim.getTime() - hoje.getTime();
      const diasRestantes = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return diasRestantes;
    } catch (_error) {
      console.error("❌ Erro ao calcular dias restantes:", error);
      return null;
    }
  };

  // Métricas calculadas
  const progressoReal = calcularProgressoObra();
  const totalGastos = calcularTotalGastos();
  const diasRestantes = calcularDiasRestantes();
  const percentualGasto = calcularPercentualGasto();

  // ============================================================================
  // SISTEMA DE NOTIFICAÇÕES AUTOMÁTICAS
  // ============================================================================

  // Verificar prazo da obra
  useEffect(() => {
    if (!obra?.id || diasRestantes === null) return;

    // Notificar quando restam 7 dias ou menos
    if (diasRestantes > 0 && diasRestantes <= 7) {
      sendObraNotification(
        "obra_prazo_vencendo",
        "Obra com prazo próximo ao vencimento",
        `A obra "${obra.nome}" tem apenas ${diasRestantes} dia(s) restante(s) para conclusão`,
        {
          obra_nome: obra.nome,
          dias_restantes: diasRestantes,
          prazo_final: obra.data_prevista_termino,
          endereco: obra.endereco,
        }
      ).catch((error) => {
        console.error("Erro ao enviar notificação de prazo:", error);
      });
    }
  }, [
    diasRestantes,
    obra?.id,
    obra?.nome,
    obra?.data_prevista_termino,
    obra?.endereco,
    // Removido sendObraNotification das dependências para evitar re-execuções
  ]);

  // Verificar orçamento excedido
  useEffect(() => {
    if (!obra?.id || percentualGasto === null || totalGastos === 0) return;

    // Notificar quando exceder 100% do orçamento
    if (percentualGasto > 100) {
      sendObraNotification(
        "obra_orcamento_excedido",
        "Orçamento da obra excedido",
        `A obra "${obra.nome}" já gastou ${percentualGasto.toFixed(
          1
        )}% do orçamento previsto (R$ ${totalGastos.toLocaleString("pt-BR", {
          minimumFractionDigits: 2,
        })})`,
        {
          obra_nome: obra.nome,
          orcamento_original: obra.orcamento,
          valor_atual: totalGastos,
          percentual_excesso: percentualGasto - 100,
          diferenca_valor: totalGastos - obra.orcamento,
        }
      ).catch((error) => {
        console.error("Erro ao enviar notificação de orçamento:", error);
      });
    }
    // Notificar quando atingir 90% do orçamento (aviso preventivo)
    else if (percentualGasto >= 90) {
      sendObraNotification(
        "obra_orcamento_excedido",
        "Orçamento da obra próximo do limite",
        `A obra "${obra.nome}" já utilizou ${percentualGasto.toFixed(
          1
        )}% do orçamento previsto`,
        {
          obra_nome: obra.nome,
          orcamento_original: obra.orcamento,
          valor_atual: totalGastos,
          percentual_usado: percentualGasto,
          margem_restante: obra.orcamento - totalGastos,
        }
      ).catch((error) => {
        console.error("Erro ao enviar notificação preventiva:", error);
      });
    }
  }, [
    percentualGasto,
    totalGastos,
    obra?.id,
    obra?.nome,
    obra?.orcamento,
    // Removido sendObraNotification das dependências para evitar re-execuções
  ]);

  // Tabs dinâmicas baseadas no tipo de obra
  const tabs = [
    {
      id: "detalhes",
      label: "Detalhes",
      icon: Building,
      color: "text-blue-500",
    },
    ...(obra?.tipo_projeto === "CONDOMINIO_MASTER"
      ? [
          {
            id: "unidades",
            label: "Unidades",
            icon: Home,
            color: "text-purple-500",
          },
        ]
      : []),
    {
      id: "despesas",
      label: "Despesas",
      icon: Receipt,
      color: "text-orange-500",
    },
    {
      id: "venda",
      label: "Venda e Lucro",
      icon: DollarSign,
      color: "text-green-500",
    },
    {
      id: "insights",
      label: "Insights IA",
      icon: TrendingUp,
      color: "text-blue-500",
    },
    {
      id: "chat",
      label: "Chat IA",
      icon: MessageSquare,
      color: "text-green-500",
    },
  ];

  if (isLoading) {
    return (
      <DashboardLayout>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center justify-center h-96"
        >
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
            <p className="text-muted-foreground">
              Carregando detalhes da obra...
            </p>
          </div>
        </motion.div>
      </DashboardLayout>
    );
  }

  if (isError || !obra) {
    return (
      <DashboardLayout>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col items-center justify-center h-96 space-y-4"
        >
          <div className="h-16 w-16 rounded-full bg-red-500/10 flex items-center justify-center">
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
          <div className="text-center space-y-2">
            <h3 className="text-lg font-semibold">Erro ao carregar obra</h3>
            <p className="text-muted-foreground">
              Não foi possível encontrar os dados da obra solicitada.
            </p>
          </div>
          <Button
            onClick={() => navigate("/dashboard/obras")}
            variant="outline"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar para lista
          </Button>
        </motion.div>
      </DashboardLayout>
    );
  }

  const despesasColumns: ColumnDef<Despesa>[] = [
    {
      accessorKey: "descricao",
      header: "Descrição",
      cell: ({ row }) => {
        const despesa = row.original as any;
        const isRateado = despesa._isRateado;
        const isEspecifica = despesa.tipo_despesa === "ESPECIFICA";
        const unidadeInfo = despesa.unidade_obra;

        return (
          <div className="space-y-1">
            <div className="font-medium">{row.original.descricao}</div>
            <div className="flex items-center gap-2 flex-wrap">
              {isRateado && (
                <>
                  <Badge
                    variant="secondary"
                    className="text-xs bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300"
                  >
                    Rateado ({Math.round(despesa._proporcao * 100)}%)
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    Original: {formatCurrencyBR(despesa._custoOriginal || 0)}
                  </span>
                </>
              )}
              {isEspecifica && !isRateado && (
                <>
                  <Badge
                    variant="outline"
                    className="text-xs bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-700 text-orange-700 dark:text-orange-300"
                  >
                    Específica
                  </Badge>
                  {unidadeInfo && (
                    <span className="text-xs text-muted-foreground">
                      {unidadeInfo.identificador_unidade || unidadeInfo.nome}
                    </span>
                  )}
                </>
              )}
              {despesa.tipo_despesa === "COMUM" && !isRateado && (
                <Badge
                  variant="secondary"
                  className="text-xs bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-700 dark:text-green-300"
                >
                  Comum
                </Badge>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "categoria",
      header: "Categoria",
      cell: ({ row }) => (
        <Badge
          variant="outline"
          className="text-xs bg-violet-50 dark:bg-violet-900/20 border-violet-200 dark:border-violet-700 text-violet-700 dark:text-violet-300"
        >
          {row.original.categoria || "-"}
        </Badge>
      ),
    },
    {
      accessorKey: "custo",
      header: "Valor",
      cell: ({ row }) => (
        <span className="font-mono font-medium text-emerald-600 dark:text-emerald-400">
          {formatCurrencyBR(row.original.custo)}
        </span>
      ),
    },
    {
      accessorKey: "data_despesa",
      header: "Data",
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {formatDateBR(row.original.data_despesa)}
        </span>
      ),
    },
    {
      accessorKey: "pago",
      header: "Status",
      cell: ({ row }) =>
        row.original.pago ? (
          <Badge className="bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-700 hover:bg-emerald-200 dark:hover:bg-emerald-900/50">
            <Check className="h-3 w-3 mr-1" />
            Pago
          </Badge>
        ) : (
          <Badge
            variant="outline"
            className="bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-700 text-amber-700 dark:text-amber-300"
          >
            <AlertTriangle className="h-3 w-3 mr-1" />
            Pendente
          </Badge>
        ),
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            title="Editar"
            onClick={() =>
              navigate(
                `/dashboard/despesas/${row.original.id}/editar?return=/dashboard/obras/${id}`
              )
            }
            className="h-8 w-8 text-sky-600 dark:text-sky-400 hover:bg-sky-100 dark:hover:bg-sky-900/30 hover:text-sky-700 dark:hover:text-sky-300 transition-colors"
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            title="Excluir"
            onClick={() => setDespesaToDelete(row.original.id)}
            className="h-8 w-8 text-rose-600 dark:text-rose-400 hover:bg-rose-100 dark:hover:bg-rose-900/30 hover:text-rose-700 dark:hover:text-rose-300 transition-colors"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  const handleDeleteDespesa = async () => {
    if (!despesaToDelete) return;
    await deleteDespesa.mutateAsync(despesaToDelete, {
      onSuccess: () => {
        toast({
          title: "Sucesso!",
          description: "A despesa foi excluída.",
          variant: "success",
        });
        setDespesaToDelete(null);
        // O onSuccess da query já vai refetch os dados da obra
      },
      onError: (error) => {
        toast({
          title: "Erro ao excluir",
          description: error.message || "Não foi possível remover a despesa.",
          variant: "destructive",
        });
      },
    });
  };

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="flex items-center gap-3"
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/dashboard/obras")}
              className="hover:bg-blue-500/10 group"
            >
              <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
            </Button>
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-blue-500/10 dark:bg-blue-400/10 flex items-center justify-center">
                {obra.tipo_projeto === "CONDOMINIO_MASTER" ? (
                  <Home className="h-6 w-6 text-blue-500 dark:text-blue-400" />
                ) : obra.tipo_projeto === "UNIDADE_CONDOMINIO" ? (
                  <Home className="h-6 w-6 text-purple-500 dark:text-purple-400" />
                ) : (
                  <Building className="h-6 w-6 text-blue-500 dark:text-blue-400" />
                )}
              </div>
              <div>
                <div className="flex items-center gap-2">
                  {obra.tipo_projeto === "UNIDADE_CONDOMINIO" &&
                    condominioPai && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            navigate(`/dashboard/obras/${condominioPai.id}`)
                          }
                          className="text-sm text-muted-foreground hover:text-foreground p-0 h-auto"
                        >
                          {condominioPai.nome}
                        </Button>
                        <span className="text-muted-foreground">/</span>
                      </>
                    )}
                  <h1 className="text-2xl font-bold">
                    {obra.identificador_unidade || obra.nome}
                  </h1>
                  {obra.tipo_projeto === "CONDOMINIO_MASTER" && (
                    <Badge variant="secondary" className="ml-2">
                      Condomínio
                    </Badge>
                  )}
                  {obra.tipo_projeto === "UNIDADE_CONDOMINIO" && (
                    <Badge variant="outline" className="ml-2">
                      Unidade
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  {obra.cidade}, {obra.estado}
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="flex gap-2"
          >
            <Button
              variant="outline"
              onClick={() =>
                navigate(
                  `/dashboard/orcamentos/novo?obra_id=${id}&return=/dashboard/obras/${id}`
                )
              }
              className="group hover:border-green-500/50 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400"
            >
              <Calculator className="h-4 w-4 mr-2 transition-colors group-hover:text-green-500" />
              Orçamento IA
            </Button>
            <Button
              variant="outline"
              onClick={() =>
                navigate(
                  `/dashboard/despesas/nova?obra_id=${id}&return=/dashboard/obras/${id}`
                )
              }
              className="group hover:border-blue-500/50 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400"
            >
              <Plus className="h-4 w-4 mr-1 transition-colors group-hover:text-blue-500" />
              <Receipt className="h-3 w-3 mr-2 transition-colors group-hover:text-blue-500" />
              Nova Despesa
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate(`/dashboard/obras/${id}/editar`)}
              className="group hover:border-blue-500/50"
            >
              <Pencil className="h-4 w-4 mr-2 transition-colors group-hover:text-blue-500" />
              Editar Obra
            </Button>
          </motion.div>
        </div>

        {/* Status Badge */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Badge
            variant="secondary"
            className="bg-green-500/10 text-green-700 dark:text-green-400"
          >
            <div className="h-2 w-2 rounded-full bg-green-500 mr-2 animate-pulse" />
            Em Andamento
          </Badge>
        </motion.div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList
              className={`grid w-full ${
                obra?.tipo_projeto === "CONDOMINIO_MASTER"
                  ? "grid-cols-6"
                  : "grid-cols-5"
              }`}
            >
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex items-center gap-2"
                  >
                    <Icon className={cn("h-4 w-4", tab.color)} />
                    {tab.label}
                  </TabsTrigger>
                );
              })}
            </TabsList>

            <TabsContent value="detalhes" className="space-y-6 mt-6">
              {/* Dashboard específico para condomínio */}
              {obra?.tipo_projeto === "CONDOMINIO_MASTER" && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="mb-8"
                >
                  <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Home className="h-5 w-5 text-blue-500" />
                    Dashboard do Condomínio
                  </h2>
                  <CondominioDashboard obraId={id!} />
                </motion.div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <div className="h-8 w-8 rounded-lg bg-blue-500/10 flex items-center justify-center">
                          <MapPin className="h-4 w-4 text-blue-500" />
                        </div>
                        Informações de Localização
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <dl className="space-y-3">
                        <div className="flex justify-between items-start">
                          <dt className="text-sm text-muted-foreground">
                            Endereço
                          </dt>
                          <dd className="text-right max-w-[60%] font-medium">
                            {obra.endereco}
                          </dd>
                        </div>
                        <div className="flex justify-between items-center">
                          <dt className="text-sm text-muted-foreground">
                            Cidade
                          </dt>
                          <dd className="font-medium">{obra.cidade}</dd>
                        </div>
                        <div className="flex justify-between items-center">
                          <dt className="text-sm text-muted-foreground">
                            Estado
                          </dt>
                          <dd className="font-medium">{obra.estado}</dd>
                        </div>
                        <div className="flex justify-between items-center">
                          <dt className="text-sm text-muted-foreground">CEP</dt>
                          <dd className="font-mono text-sm">{obra.cep}</dd>
                        </div>
                      </dl>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-lg bg-yellow-500/10 flex items-center justify-center">
                            <Clock className="h-4 w-4 text-yellow-500" />
                          </div>
                          Cronograma e Orçamento
                        </div>
                        {(!obra.data_inicio || !obra.data_prevista_termino) && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              navigate(`/dashboard/obras/${id}/editar`)
                            }
                            className="text-xs text-amber-600 border-amber-200 dark:border-amber-800 hover:bg-amber-50 dark:hover:bg-amber-900/20"
                          >
                            <Calendar className="h-3 w-3 mr-1" />
                            Definir Datas
                          </Button>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <dl className="space-y-3">
                        <div className="flex justify-between items-center">
                          <dt className="text-sm text-muted-foreground flex items-center gap-2">
                            <Calendar className="h-3 w-3" />
                            Data de Início
                          </dt>
                          <dd className="font-medium">
                            {obra.data_inicio ? (
                              formatDateBR(obra.data_inicio)
                            ) : (
                              <span className="text-muted-foreground text-sm italic">
                                Não definida
                              </span>
                            )}
                          </dd>
                        </div>
                        <div className="flex justify-between items-center">
                          <dt className="text-sm text-muted-foreground flex items-center gap-2">
                            <Calendar className="h-3 w-3" />
                            Previsão de Término
                          </dt>
                          <dd className="font-medium">
                            {obra.data_prevista_termino ? (
                              formatDateBR(obra.data_prevista_termino)
                            ) : (
                              <span className="text-muted-foreground text-sm italic">
                                Não definida
                              </span>
                            )}
                          </dd>
                        </div>
                        <div className="flex justify-between items-center pt-2 border-t">
                          <dt className="text-sm text-muted-foreground flex items-center gap-2">
                            <DollarSign className="h-3 w-3" />
                            Investimento Total
                          </dt>
                          <dd className="font-semibold text-blue-600 dark:text-blue-400">
                            {formatCurrencyBR(obra.orcamento)}
                          </dd>
                        </div>

                        {/* Custo do Terreno */}
                        {obra.custo_terreno && obra.custo_terreno > 0 && (
                          <div className="flex justify-between items-center pt-2 border-t">
                            <dt className="text-sm text-muted-foreground flex items-center gap-2">
                              <MapPin className="h-3 w-3" />
                              Custo do Terreno
                            </dt>
                            <dd className="font-semibold text-red-600 dark:text-red-400">
                              - {formatCurrencyBR(obra.custo_terreno)}
                            </dd>
                          </div>
                        )}

                        <div className="flex justify-between items-center pt-2 border-t bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 -mx-4 px-4 py-2 rounded-lg">
                          <dt className="text-sm font-medium text-green-700 dark:text-green-300 flex items-center gap-2">
                            <Building className="h-4 w-4" />
                            Capital para Construção
                          </dt>
                          <dd className="font-bold text-xl bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                            {formatCurrencyBR(calcularCapitalConstrucao())}
                          </dd>
                        </div>
                      </dl>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              {/* Cards de métricas com dados reais */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-4"
              >
                {/* Card Progresso - Baseado no cronograma */}
                <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center">
                        <TrendingUp className="h-5 w-5 text-green-500" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Progresso
                        </p>
                        <p className="text-xl font-bold">
                          {`${progressoReal}%`}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {obra?.data_inicio && obra?.data_prevista_termino
                            ? "Baseado no cronograma"
                            : "Aguardando datas da obra"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Card Gastos - Baseado nas despesas reais */}
                <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center">
                        {isLoadingDespesas ? (
                          <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
                        ) : (
                          <BarChart3 className="h-5 w-5 text-blue-500" />
                        )}
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Gastos Reais
                        </p>
                        <p className="text-xl font-bold">
                          {isLoadingDespesas ? (
                            <span className="text-muted-foreground">
                              Carregando...
                            </span>
                          ) : (
                            formatCurrencyBR(totalGastos)
                          )}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {isLoadingDespesas ? (
                            "Buscando despesas..."
                          ) : (
                            <>
                              {`${despesasObra?.length || 0} despesa(s)`}
                              {percentualGasto !== null && obra?.orcamento && (
                                <span
                                  className={cn(
                                    "ml-2 font-medium",
                                    percentualGasto > 100
                                      ? "text-red-500"
                                      : percentualGasto > 80
                                      ? "text-amber-500"
                                      : "text-green-500"
                                  )}
                                >
                                  ({percentualGasto}% do orçado)
                                </span>
                              )}
                            </>
                          )}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Card Dias Restantes - Calculado da data fim */}
                <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center">
                        <Clock className="h-5 w-5 text-blue-500" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Dias Restantes
                        </p>
                        <p className="text-xl font-bold">
                          {diasRestantes !== null ? (
                            diasRestantes > 0 ? (
                              diasRestantes
                            ) : diasRestantes === 0 ? (
                              "Hoje"
                            ) : (
                              <span className="text-red-500">
                                {Math.abs(diasRestantes)} em atraso
                              </span>
                            )
                          ) : (
                            <span className="text-muted-foreground text-base">
                              Sem prazo
                            </span>
                          )}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {obra?.data_prevista_termino
                            ? `Até ${formatDateBR(obra.data_prevista_termino)}`
                            : "Defina datas da obra"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Seção de Orçamentos Relacionados */}
              {orcamentosRelacionados && orcamentosRelacionados.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                >
                  <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <div className="h-8 w-8 rounded-lg bg-green-500/10 flex items-center justify-center">
                          <Calculator className="h-4 w-4 text-green-500" />
                        </div>
                        Orçamentos Paramétricos
                        <Badge variant="secondary" className="ml-auto">
                          {orcamentosRelacionados.length}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {orcamentosRelacionados.map((orcamento) => (
                          <div
                            key={orcamento.id}
                            className="flex items-center justify-between p-3 rounded-lg border border-border/50 bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer"
                            onClick={() =>
                              navigate(`/dashboard/orcamentos/${orcamento.id}`)
                            }
                          >
                            <div className="space-y-1">
                              <p className="font-medium">
                                {orcamento.nome_orcamento}
                              </p>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Badge variant="outline" className="text-xs">
                                  {TIPO_OBRA_LABELS[orcamento.tipo_obra]}
                                </Badge>
                                <span>•</span>
                                <span>{orcamento.area_total} m²</span>
                              </div>
                            </div>
                            <div className="text-right space-y-1">
                              <p className="font-bold text-green-600 dark:text-green-400">
                                {orcamentoUtils.formatarValor(
                                  orcamento.custo_estimado
                                )}
                              </p>
                              <Badge
                                className={cn(
                                  "text-xs",
                                  STATUS_ORCAMENTO_CORES[orcamento.status]
                                )}
                              >
                                {STATUS_ORCAMENTO_LABELS[orcamento.status]}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </TabsContent>

            {/* Tab específica para unidades do condomínio */}
            {obra?.tipo_projeto === "CONDOMINIO_MASTER" && (
              <TabsContent value="unidades" className="pt-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-xl font-semibold">
                          Unidades do Condomínio
                        </h2>
                        <p className="text-sm text-muted-foreground">
                          Gerencie as unidades deste condomínio
                        </p>
                      </div>
                      <Button
                        onClick={() =>
                          navigate(
                            `/dashboard/obras/nova?parent_id=${id}&tipo=UNIDADE_CONDOMINIO`
                          )
                        }
                        className="bg-[#daa916] hover:bg-[#c99914] text-black"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Nova Unidade
                      </Button>
                    </div>

                    {isLoadingUnidades ? (
                      <div className="flex items-center justify-center h-48">
                        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                      </div>
                    ) : (
                      <ListaUnidades
                        condomínioId={id!}
                        unidades={unidadesCondominio || []}
                        isLoading={isLoadingUnidades}
                      />
                    )}
                  </div>
                </motion.div>
              </TabsContent>
            )}

            <TabsContent value="despesas" className="pt-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Despesas da Obra</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {isLoadingDespesas ? (
                      <div className="flex items-center justify-center h-48">
                        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                      </div>
                    ) : (
                      <DataTable
                        columns={despesasColumns}
                        data={despesasObra || []}
                        searchColumn="descricao"
                        searchPlaceholder="Buscar por descrição..."
                      />
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="insights" className="pt-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <InsightsObra obraId={id!} />
              </motion.div>
            </TabsContent>

            <TabsContent value="chat" className="pt-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <InterfaceChat obraId={id!} />
              </motion.div>
            </TabsContent>

            <TabsContent value="venda" className="pt-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <VendaLucroTab obraId={id!} />
              </motion.div>
            </TabsContent>
          </Tabs>
        </motion.div>
      </motion.div>

      <AlertDialog
        open={!!despesaToDelete}
        onOpenChange={(open) => !open && setDespesaToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir esta despesa? Esta ação não pode
              ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDespesa}
              className="bg-destructive hover:bg-destructive/90"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DashboardLayout>
  );
};

export default ObraDetalhe;
