# 🚨 RESUMO COMPLETO - PROBLEMAS NO FLUXO DE REGISTRO FREE TRIAL

## 📋 CONTEXTO GERAL

**Objetivo**: Implementar e testar fluxo completo de registro para free trial de 7 dias
**Status**: ❌ FALHANDO - Erro 500 durante registro de usuários
**Data**: 31/07/2025
**Última Atualização**: 31/07/2025 - 13:00

---

## 🔍 PROBLEMA PRINCIPAL

### **Erro 500 durante registro de usuários**

- **Sintoma**: Ao tentar registrar novo usuário via formulário, retorna erro 500
- **Localização**: Falha na criação do usuário no Supabase Auth
- **Impacto**: Sistema completamente inacessível para novos usuários
- **URL Teste**: http://localhost:8080/register

### **Mensagens de Erro Observadas**

```
Failed to load resource: the server responded with a status of 500
[ERROR] Registration failed {email: [REDACTED], error: Object}
Database error saving new user
```

### **Console Logs Detalhados**

```javascript
[INFO] Registration attempt {email: [REDACTED]}
[ERROR] Failed to load resource: the server responded with a status of 500 () @ https://anrphijuostbgbscxmzx.supabase.co/auth/v1/signup
[ERROR] Registration failed {email: [REDACTED], error: Object}
```

---

## 🛠️ EDGE FUNCTIONS - STATUS

### ✅ **DEPLOYADAS COM SUCESSO**

1. **create-trial-subscription**

   - Status: ✅ Deployada (135.3kB)
   - URL: `https://anrphijuostbgbscxmzx.supabase.co/functions/v1/create-trial-subscription`
   - Função: Criar trials de 7 dias para novos usuários
   - Modificação: Removida exigência de autenticação (`requiresAuth: false`)

2. **check-expired-trials**
   - Status: ✅ Deployada (134.3kB)
   - URL: `https://anrphijuostbgbscxmzx.supabase.co/functions/v1/check-expired-trials`
   - Função: Verificar e processar trials expirados

### **Comandos de Deploy Executados**

```bash
npx supabase functions deploy create-trial-subscription --no-verify-jwt
npx supabase functions deploy check-expired-trials --no-verify-jwt
```

---

## 🗄️ PROBLEMAS DE BANCO DE DADOS IDENTIFICADOS

### **1. Função `handle_new_user` Problemática**

**Localização**: `supabase/migrations/0000_initial_schema.sql` (linha 989-1001)
**Problema**: Função tentava inserir na tabela `profiles` sem fornecer `tenant_id` obrigatório

**Código Original (Problemático)**:

```sql
CREATE OR REPLACE FUNCTION "public"."handle_new_user"()
RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name)  -- ❌ Faltava tenant_id
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'given_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', NEW.raw_user_meta_data->>'family_name', '')
  );
  RETURN NEW;
END;
$$;
```

**Correção Aplicada** (Migração `20250731130000_fix_handle_new_user_function.sql`):

```sql
CREATE OR REPLACE FUNCTION "public"."handle_new_user"()
RETURNS "trigger" LANGUAGE "plpgsql" SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, tenant_id)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'given_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', NEW.raw_user_meta_data->>'family_name', ''),
    gen_random_uuid() -- ✅ Gerar tenant_id único para cada novo usuário
  );
  RETURN NEW;
END;
$$;
```

### **2. Constraint Problemática na Tabela `subscriptions`**

**Localização**: `supabase/migrations/20250705170000_create_subscriptions_table.sql` (linha 18)
**Problema**: Constraint `UNIQUE(user_id, status)` impedia múltiplas assinaturas
**Correção**: Criado índice específico para assinaturas ativas apenas

**Migração Aplicada** (`20250731120000_fix_subscriptions_constraint.sql`):

```sql
-- Remove a constraint problemática
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_user_id_status_key;

-- Adicionar constraint mais específica: apenas uma assinatura ativa por usuário
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_user_active
ON subscriptions (user_id)
WHERE status IN ('active', 'trialing');
```

### **3. Múltiplos Triggers no `auth.users` Identificados**

**Triggers Confirmados**:

- `trigger_create_default_preferences` → `create_default_user_preferences()` (linha 115-118, migração 20250711030000)
- `create_notification_preferences_on_signup` → `create_default_notification_preferences()` (linha 183-185, migração 20250711060000)

**⚠️ TRIGGER CRÍTICO FALTANTE**: Não conseguimos localizar o trigger que chama `handle_new_user` no `auth.users`

### **4. Tabela `profiles` - Estrutura Atual**

```sql
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  first_name TEXT,
  last_name TEXT,
  tenant_id UUID NOT NULL, -- ⚠️ Campo obrigatório
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Trigger na tabela `profiles`**:

- `trigger_generate_tenant_id_profiles` → `generate_tenant_id_for_new_user()` (BEFORE INSERT)

---

## 🔧 MIGRAÇÕES APLICADAS

### **Migração 1**: `20250731120000_fix_subscriptions_constraint.sql`

- ✅ Status: Aplicada com sucesso
- Remove constraint problemática `UNIQUE(user_id, status)`
- Adiciona índice específico para assinaturas ativas

### **Migração 2**: `20250731130000_fix_handle_new_user_function.sql`

- ✅ Status: Aplicada com sucesso
- Corrige função `handle_new_user` para incluir `tenant_id`
- Adiciona geração automática de `tenant_id` único

**Comandos Executados**:

```bash
npx supabase migration repair --status applied 20250731120000
npx supabase migration repair --status applied 20250731130000
```

---

## 🧪 TESTES REALIZADOS COM PLAYWRIGHT

### **Ambiente de Teste**

- **URL**: http://localhost:8080 (servidor local rodando)
- **Comando**: `npm run dev` (porta 8080)
- **Browser**: Playwright automatizado

### **Teste 1: Registro Completo com Trial**

- **Dados**: João Silva, <EMAIL>, MinhaSenh@123
- **Resultado**: ❌ Erro 500
- **Observação**: Erro na criação do usuário no Supabase Auth

### **Teste 2: Registro Completo (Usuários Diferentes)**

- **Dados Testados**:
  - Maria Santos, <EMAIL>
  - Carlos Oliveira, <EMAIL>
  - Ana Costa, <EMAIL>
  - Pedro Almeida, <EMAIL>
- **Resultado**: ❌ Todos falharam com erro 500
- **Padrão**: Erro consistente independente dos dados

### **Teste 3: Registro Sem Trial (Edge Function Desabilitada)**

- **Modificação**: Comentado `createTrialSubscription` no `RegisterForm.tsx`
- **Resultado**: ❌ Erro 500 persiste
- **Conclusão**: Problema não está na Edge Function, mas no próprio registro do Supabase Auth

### **Teste 4: Edge Function Direta**

- **Comando PowerShell**:

```powershell
Invoke-WebRequest -Uri "https://anrphijuostbgbscxmzx.supabase.co/functions/v1/create-trial-subscription"
-Method POST -Headers @{"Authorization"="Bearer [ANON_KEY]"; "Content-Type"="application/json"}
-Body '{"userId":"test-user-id"}'
```

- **Resultado**: ❌ Erro 500
- **Confirmação**: Edge Function também tem problemas, mas não é a causa raiz

---

## 🔍 INVESTIGAÇÃO ADICIONAL NECESSÁRIA

### **1. Trigger Faltante no `auth.users`**

**Problema Crítico**: Não conseguimos localizar o trigger que chama `handle_new_user`
**Necessário**:

```sql
-- Procurar por triggers no schema auth:
SELECT
  trigger_name,
  event_manipulation,
  action_statement,
  action_timing
FROM information_schema.triggers
WHERE event_object_schema = 'auth'
AND event_object_table = 'users';

-- Verificar se existe trigger específico:
SELECT * FROM pg_trigger
WHERE tgname LIKE '%handle_new_user%';
```

### **2. Logs Detalhados do Supabase**

**Problema**: MCP do Supabase não está conectando para acessar logs
**Tentativas Falharam**:

```bash
get_logs_supabase(service="api")      # ❌ Not connected
get_logs_supabase(service="postgres") # ❌ Not connected
get_logs_supabase(service="auth")     # ❌ Not connected
```

### **3. Schema `auth` Completo**

**Problema**: Pode haver triggers ou funções no schema `auth` que não estão visíveis no schema público
**Necessário**: Investigar schema `auth` completo via SQL direto

### **4. RLS Policies Potencialmente Problemáticas**

**Suspeita**: Policies podem estar bloqueando inserções na tabela `profiles`
**Verificar**:

```sql
-- Listar policies da tabela profiles:
SELECT * FROM pg_policies WHERE tablename = 'profiles';

-- Verificar se RLS está habilitado:
SELECT relname, relrowsecurity FROM pg_class WHERE relname = 'profiles';
```

---

## 📁 ARQUIVOS MODIFICADOS DURANTE DEBUGGING

### **Frontend**

1. **`src/components/auth/RegisterForm.tsx`**

   - Linha 81-84: Temporariamente comentado `createTrialSubscription`
   - Objetivo: Isolar problema da Edge Function vs Supabase Auth

2. **`src/contexts/auth/AuthContext.tsx`**
   - Linha 286-315: Lógica de registro com metadata
   - Observação: Função `register` está correta

### **Backend - Edge Functions**

1. **`supabase/functions/create-trial-subscription/index.ts`**
   - Linha 20: `requiresAuth: false` (removida exigência de autenticação)
   - Linha 40-58: Adicionada validação condicional de userId vs auth

### **Backend - Migrações**

1. **`supabase/migrations/20250731120000_fix_subscriptions_constraint.sql`** (NOVO)
2. **`supabase/migrations/20250731130000_fix_handle_new_user_function.sql`** (NOVO)

### **Configurações**

1. **`.env`** - Verificado, variáveis corretas:
   - `VITE_SUPABASE_URL=https://anrphijuostbgbscxmzx.supabase.co`
   - `VITE_SUPABASE_ANON_KEY=[CORRETO]`

---

## 🎯 PRÓXIMAS AÇÕES RECOMENDADAS

### **PRIORIDADE CRÍTICA (Resolver Hoje)**

1. **🔍 Encontrar trigger faltante**: Executar SQL para localizar trigger que chama `handle_new_user`
2. **📊 Acessar logs do Supabase**: Via dashboard web se MCP não funcionar
3. **🧪 Teste manual no dashboard**: Criar usuário diretamente no Supabase Dashboard

### **PRIORIDADE ALTA**

1. **🔐 Verificar RLS policies**: Podem estar bloqueando inserções na tabela `profiles`
2. **🔄 Revisar todas as funções**: Que são executadas durante registro de usuário
3. **🗄️ Verificar integridade do banco**: Possível corrupção de função ou trigger

### **PRIORIDADE MÉDIA**

1. **📝 Testar com usuário simples**: Sem metadata adicional (first_name, last_name)
2. **🔧 Verificar permissões**: No schema `auth` e `public`
3. **📋 Documentar schema completo**: Para referência futura

### **DEBUGGING SQL SUGERIDO**

```sql
-- 1. Listar TODOS os triggers em auth.users
SELECT
  t.trigger_name,
  t.event_manipulation,
  t.action_timing,
  t.action_statement,
  p.proname as function_name
FROM information_schema.triggers t
LEFT JOIN pg_proc p ON p.oid = t.action_statement::regproc
WHERE t.event_object_schema = 'auth'
AND t.event_object_table = 'users';

-- 2. Verificar se handle_new_user existe e está correta
SELECT
  proname,
  prosrc,
  proowner,
  proacl
FROM pg_proc
WHERE proname = 'handle_new_user';

-- 3. Testar inserção manual na tabela profiles
INSERT INTO public.profiles (id, first_name, last_name, tenant_id)
VALUES (gen_random_uuid(), 'Teste', 'Manual', gen_random_uuid());

-- 4. Verificar RLS na tabela profiles
SELECT
  schemaname,
  tablename,
  rowsecurity,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'profiles';

-- 5. Verificar se auth.users tem triggers
\d+ auth.users
```

---

## 💡 HIPÓTESES PARA O ERRO (Ordenadas por Probabilidade)

### **1. Trigger Ausente ou Corrompido (90% probabilidade)**

- **Hipótese**: O trigger que chama `handle_new_user` não existe ou está corrompido
- **Evidência**: Não conseguimos localizar o trigger no schema
- **Solução**: Criar o trigger manualmente

### **2. RLS Policy Bloqueando Inserção (70% probabilidade)**

- **Hipótese**: Alguma policy RLS está impedindo a inserção na tabela `profiles`
- **Evidência**: Erro 500 sugere problema de permissão
- **Solução**: Revisar e ajustar policies

### **3. Função `handle_new_user` Corrompida (60% probabilidade)**

- **Hipótese**: A função existe mas tem erro interno não visível
- **Evidência**: Mesmo com correção aplicada, erro persiste
- **Solução**: Recriar função completamente

### **4. Dependência Circular entre Triggers (40% probabilidade)**

- **Hipótese**: Múltiplos triggers estão criando dependência circular
- **Evidência**: Vários triggers executam no `auth.users`
- **Solução**: Revisar ordem de execução dos triggers

### **5. Problema no Schema `auth` (30% probabilidade)**

- **Hipótese**: Há algum problema no schema `auth` que não conseguimos ver
- **Evidência**: Erro vem do endpoint `/auth/v1/signup`
- **Solução**: Investigar schema `auth` diretamente

### **6. Configuração do Supabase (20% probabilidade)**

- **Hipótese**: Problema na configuração do projeto Supabase
- **Evidência**: Erro 500 genérico
- **Solução**: Verificar configurações no dashboard

---

## 🚨 STATUS ATUAL DO SISTEMA

### **✅ FUNCIONANDO**

- ✅ Edge Functions deployadas e acessíveis
- ✅ Banco de dados conectado e responsivo
- ✅ Frontend carregando corretamente
- ✅ Formulário de registro capturando dados

### **❌ NÃO FUNCIONANDO**

- ❌ Registro de novos usuários (erro 500)
- ❌ Criação automática de trials
- ❌ Acesso de novos usuários ao sistema
- ❌ Logs detalhados via MCP

### **⚠️ IMPACTO NO NEGÓCIO**

- **Crítico**: Sistema inacessível para novos usuários
- **Receita**: Impossível capturar novos leads/trials
- **Reputação**: Experiência ruim para usuários tentando se registrar
- **Urgência**: Resolução necessária em 24h

---

**⚠️ URGENTE**: O sistema está completamente inacessível para novos usuários. Prioridade máxima para resolução imediata.

**🔧 COMANDO PARA CLAUDE CODE**:

```
Analise este resumo detalhado e identifique a causa raiz do erro 500 no registro de usuários.
Foque especialmente em:
1. Encontrar o trigger faltante que chama handle_new_user
2. Verificar RLS policies na tabela profiles
3. Testar inserção manual na tabela profiles
4. Criar solução definitiva para o problema
```
