import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.29.0";
import { checkAndIncrementAIUsage } from "../_shared/ai-quota-checker.ts";
import { getSecureCorsHeaders, getPreflightHeaders } from "../_shared/cors.ts";

// ✅ ETAPA 4: Edge Function com CORS + Autenticação + Validação Zod
// Objetivo: Validar dados de entrada com schemas robustos

// Configuração do Supabase
const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;

// Schema de validação SIMPLIFICADO para requisições do chat
const chatRequestSchema = z
  .object({
    message: z.string().min(1, "Mensagem não pode estar vazia"),
    obra_id: z.any().optional(), // Aceita qualquer valor para obra_id
    user_id: z.string().min(1, "User ID obrigatório"), // Simplificado
  })
  .passthrough(); // Permite campos extras sem validar

// CORS headers agora utilizando configuração centralizada

// Função para extrair token JWT do header Authorization
function extractToken(req: Request): string | null {
  const authHeader = req.headers.get("authorization");
  if (!authHeader) return null;

  const parts = authHeader.split(" ");
  if (parts.length !== 2 || parts[0] !== "Bearer") return null;

  return parts[1];
}

// Função para validar token e obter dados do usuário
async function validateUser(token: string) {
  try {
    console.log("🔍 Criando cliente Supabase para validação...");
    console.log(
      "🔍 URL:",
      supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : "UNDEFINED"
    );
    console.log(
      "🔍 Service Key:",
      supabaseServiceKey
        ? `${supabaseServiceKey.substring(0, 30)}...`
        : "UNDEFINED"
    );

    if (!supabaseUrl || !supabaseServiceKey) {
      console.log("❌ Variáveis de ambiente não configuradas!");
      return null;
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    console.log("✅ Cliente Supabase criado com sucesso");

    console.log(
      "🔍 Validando token JWT...",
      token ? "✅ Token presente" : "❌ Token ausente"
    );
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);

    if (error) {
      console.error("❌ Token validation error:", error);
      console.error("❌ Detalhes do erro:", JSON.stringify(error, null, 2));
      return null;
    }

    if (!user) {
      console.log("❌ Usuário não encontrado no token");
      return null;
    }

    console.log("✅ Usuário autenticado com sucesso");
    return user;
  } catch (error) {
    console.error("❌ Exception during token validation:", error);
    console.error("❌ Stack trace:", error.stack);
    return null;
  }
}

// Função removida - não precisamos de embeddings para busca direta

// Função para buscar dados gerais do usuário (quando não há obra específica)
async function buscarDadosGerais(
  userId: string,
  supabase: any
): Promise<string> {
  console.log("🔍 [GERAL] Buscando dados gerais do usuário:", userId);

  try {
    // Buscar perfil do usuário para obter tenant_id
    const { data: profile } = await supabase
      .from("profiles")
      .select("tenant_id")
      .eq("id", userId)
      .single();

    if (!profile?.tenant_id) {
      return "Dados do usuário não encontrados.";
    }

    const tenantId = profile.tenant_id;
    console.log("🏢 Tenant ID encontrado:", tenantId);

    // 🔒 SEGURANÇA: Buscar APENAS dados do tenant do usuário
    const { data: obras } = await supabase
      .from("obras")
      .select("id, nome, orcamento, status, data_inicio, data_prevista_termino")
      .eq("tenant_id", tenantId); // 🔒 FILTRO DE SEGURANÇA

    const { data: despesas } = await supabase
      .from("despesas")
      .select("custo, pago, categoria, obra_id")
      .in("obra_id", obras?.map((o) => o.id) || []);

    const { data: construtoras } = await supabase
      .from("construtoras")
      .select("id, nome, telefone, email")
      .eq("tenant_id", tenantId); // 🔒 FILTRO DE SEGURANÇA

    const { data: fornecedores } = await supabase
      .from("fornecedores")
      .select("id, nome, telefone, email")
      .eq("tenant_id", tenantId); // 🔒 FILTRO DE SEGURANÇA

    // Calcular estatísticas gerais
    const totalObras = obras?.length || 0;
    const totalOrcamento =
      obras?.reduce(
        (sum, obra) => sum + (parseFloat(obra.orcamento) || 0),
        0
      ) || 0;
    const totalGastos =
      despesas?.reduce(
        (sum, despesa) => sum + (parseFloat(despesa.custo) || 0),
        0
      ) || 0;
    const totalPago =
      despesas
        ?.filter((d) => d.pago)
        .reduce((sum, despesa) => sum + (parseFloat(despesa.custo) || 0), 0) ||
      0;

    // Montar contexto
    let contexto = `=== DADOS GERAIS DO USUÁRIO ===\n\n`;
    contexto += `📊 RESUMO GERAL:\n`;
    contexto += `• Total de obras: ${totalObras}\n`;
    contexto += `• Orçamento total: R$ ${totalOrcamento.toLocaleString(
      "pt-BR",
      { minimumFractionDigits: 2 }
    )}\n`;
    contexto += `• Total gasto: R$ ${totalGastos.toLocaleString("pt-BR", {
      minimumFractionDigits: 2,
    })}\n`;
    contexto += `• Total pago: R$ ${totalPago.toLocaleString("pt-BR", {
      minimumFractionDigits: 2,
    })}\n`;
    contexto += `• Total de construtoras: ${construtoras?.length || 0}\n`;
    contexto += `• Total de fornecedores: ${fornecedores?.length || 0}\n\n`;

    if (obras && obras.length > 0) {
      contexto += `🏗️ OBRAS CADASTRADAS:\n`;
      obras.forEach((obra, index) => {
        contexto += `${index + 1}. ${obra.nome} - R$ ${(
          parseFloat(obra.orcamento) || 0
        ).toLocaleString("pt-BR", { minimumFractionDigits: 2 })}\n`;
      });
    }

    console.log("✅ [GERAL] Dados gerais obtidos com sucesso");
    return contexto;
  } catch (error) {
    console.error("❌ [GERAL] Erro ao buscar dados gerais:", error);
    return "Erro ao acessar dados gerais do usuário.";
  }
}

// Função para buscar contexto da obra DIRETAMENTE dos dados reais
async function buscarContextoObra(
  obraId: string,
  userMessage: string,
  userId: string,
  supabase: any
): Promise<string> {
  console.log("🔍 [DIRETO] Buscando dados reais da obra:", obraId);
  console.log("🔍 [DIRETO] Pergunta:", userMessage);
  console.log("🔍 [DIRETO] Validando acesso para usuário:", userId);

  // Buscar dados diretamente - SEM embeddings, SEM complicação
  return await buscarDadosBasicosObra(obraId, userId, supabase);
}

// Função para buscar dados completos da obra DIRETAMENTE
async function buscarDadosBasicosObra(
  obraId: string,
  userId: string,
  supabase: any
): Promise<string> {
  try {
    console.log("🔍 [DIRETO] Buscando dados completos da obra:", obraId);
    console.log("🔍 [DIRETO] Validando acesso para usuário:", userId);

    // 🔒 SEGURANÇA: Primeiro buscar o tenant_id do usuário
    const { data: profile } = await supabase
      .from("profiles")
      .select("tenant_id")
      .eq("id", userId)
      .single();

    if (!profile?.tenant_id) {
      console.log("❌ [SEGURANÇA] Usuário sem tenant_id válido");
      return `Acesso negado: usuário não autorizado.`;
    }

    console.log("🔒 [SEGURANÇA] Tenant ID do usuário:", profile.tenant_id);

    // 🔒 SEGURANÇA: Buscar obra APENAS do tenant do usuário
    const { data: obra, error: obraError } = await supabase
      .from("obras")
      .select("*")
      .eq("id", obraId)
      .eq("tenant_id", profile.tenant_id) // 🔒 FILTRO DE SEGURANÇA CRÍTICO
      .single();

    if (obraError || !obra) {
      console.log(
        "❌ [SEGURANÇA] Obra não encontrada ou acesso negado para tenant:",
        profile.tenant_id
      );
      return `Obra não encontrada ou sem permissão de acesso.`;
    }

    console.log(
      "✅ [SEGURANÇA] Acesso à obra autorizado para tenant:",
      profile.tenant_id
    );

    // 🔒 SEGURANÇA: Buscar APENAS despesas da obra do tenant autorizado
    const { data: despesas } = await supabase
      .from("despesas")
      .select("descricao, custo, data_despesa, categoria, pago")
      .eq("obra_id", obraId)
      .order("data_despesa", { ascending: false });

    // Calcular totais
    let totalGasto = 0;
    let totalPago = 0;
    let totalPendente = 0;
    let despesasPorCategoria: any = {};

    if (despesas && despesas.length > 0) {
      despesas.forEach((d: any) => {
        const valor = parseFloat(d.custo) || 0;
        totalGasto += valor;

        if (d.pago) {
          totalPago += valor;
        } else {
          totalPendente += valor;
        }

        // Agrupar por categoria
        const categoria = d.categoria || "OUTROS";
        if (!despesasPorCategoria[categoria]) {
          despesasPorCategoria[categoria] = { total: 0, count: 0 };
        }
        despesasPorCategoria[categoria].total += valor;
        despesasPorCategoria[categoria].count += 1;
      });
    }

    const orcamento = parseFloat(obra.orcamento) || 0;
    const restante = orcamento - totalGasto;
    const percentualGasto = orcamento > 0 ? (totalGasto / orcamento) * 100 : 0;

    // Construir contexto detalhado
    let contexto = `=== DADOS COMPLETOS DA OBRA ===\n\n`;

    contexto += `📋 INFORMAÇÕES GERAIS:\n`;
    contexto += `• Nome: ${obra.nome}\n`;
    contexto += `• Endereço: ${obra.endereco}, ${obra.cidade}/${obra.estado}\n`;
    contexto += `• CEP: ${obra.cep || "Não informado"}\n`;
    contexto += `• Data de início: ${obra.data_inicio || "Não definida"}\n`;
    contexto += `• Previsão de término: ${
      obra.data_prevista_termino || "Não definida"
    }\n\n`;

    contexto += `💰 RESUMO FINANCEIRO:\n`;
    contexto += `• Orçamento total: R$ ${orcamento.toLocaleString("pt-BR", {
      minimumFractionDigits: 2,
    })}\n`;
    contexto += `• Total gasto: R$ ${totalGasto.toLocaleString("pt-BR", {
      minimumFractionDigits: 2,
    })}\n`;
    contexto += `• Total pago: R$ ${totalPago.toLocaleString("pt-BR", {
      minimumFractionDigits: 2,
    })}\n`;
    contexto += `• Total pendente: R$ ${totalPendente.toLocaleString("pt-BR", {
      minimumFractionDigits: 2,
    })}\n`;
    contexto += `• Restante do orçamento: R$ ${restante.toLocaleString(
      "pt-BR",
      { minimumFractionDigits: 2 }
    )}\n`;
    contexto += `• Percentual gasto: ${percentualGasto.toFixed(1)}%\n`;
    contexto += `• Quantidade de despesas: ${despesas?.length || 0}\n\n`;

    // Resumo por categoria
    if (Object.keys(despesasPorCategoria).length > 0) {
      contexto += `📊 GASTOS POR CATEGORIA:\n`;
      Object.entries(despesasPorCategoria)
        .sort(([, a]: any, [, b]: any) => b.total - a.total)
        .forEach(([categoria, dados]: any) => {
          contexto += `• ${categoria}: R$ ${dados.total.toLocaleString(
            "pt-BR",
            { minimumFractionDigits: 2 }
          )} (${dados.count} itens)\n`;
        });
      contexto += "\n";
    }

    // Despesas recentes (últimas 10)
    if (despesas && despesas.length > 0) {
      const despesasRecentes = despesas.slice(0, 10);
      contexto += `📝 DESPESAS RECENTES (últimas ${despesasRecentes.length}):\n`;
      despesasRecentes.forEach((d: any, index: number) => {
        const valor = parseFloat(d.custo) || 0;
        const status = d.pago ? "✅ PAGO" : "⏳ PENDENTE";
        contexto += `${index + 1}. ${d.descricao} - R$ ${valor.toLocaleString(
          "pt-BR",
          { minimumFractionDigits: 2 }
        )} ${status} (${d.data_despesa})\n`;
      });
    }

    console.log("✅ [DIRETO] Dados completos obtidos com sucesso");
    console.log(
      `📊 [DIRETO] Resumo: ${
        despesas?.length || 0
      } despesas, R$ ${totalGasto.toLocaleString("pt-BR")}`
    );

    return contexto;
  } catch (error) {
    console.error("❌ [SEGURANÇA] Erro ao buscar dados da obra:", error);
    return "Erro ao acessar dados da obra.";
  }
}

// Função para validar dados de entrada (SIMPLIFICADA)
function validateRequestBody(body: unknown) {
  try {
    console.log("🔍 Validando body:", body);
    const validatedData = chatRequestSchema.parse(body);
    console.log("✅ Dados validados com sucesso:", validatedData);

    // Adicionar campos padrão que podem estar ausentes
    const finalData = {
      message: validatedData.message,
      obra_id: validatedData.obra_id || null,
      user_id: validatedData.user_id,
      context: "geral",
      incluir_sinapi: false,
      max_tokens: 1000,
      temperatura: 0.7,
    };

    return { success: true, data: finalData };
  } catch (error) {
    console.error("❌ Erro completo na validação:", error);
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(
        (err) => `${err.path.join(".")}: ${err.message}`
      );
      console.error("❌ Detalhes do erro Zod:", errorMessages);
      return {
        success: false,
        error: `Dados inválidos: ${errorMessages.join(", ")}`,
      };
    }
    return {
      success: false,
      error: "Erro interno na validação dos dados",
    };
  }
}

serve(async (req: Request) => {
  const origin = req.headers.get("origin");
  const corsHeaders = getSecureCorsHeaders(origin);

  console.log(
    `🚀 ai-chat-handler-v2 - Método: ${req.method}, Origin: ${origin}`
  );
  console.log(
    `🔍 Headers recebidos:`,
    Object.fromEntries(req.headers.entries())
  );
  console.log(
    `🔑 DEEPSEEK_API_KEY disponível:`,
    Deno.env.get("DEEPSEEK_API_KEY") ? "SIM" : "NÃO"
  );
  console.log(
    `🔑 SUPABASE_URL disponível:`,
    Deno.env.get("SUPABASE_URL") ? "SIM" : "NÃO"
  );
  console.log(
    `🔑 SUPABASE_SERVICE_ROLE_KEY disponível:`,
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ? "SIM" : "NÃO"
  );

  // Debug: Verificar se as variáveis estão sendo carregadas
  const supabaseUrlDebug = Deno.env.get("SUPABASE_URL");
  const supabaseServiceKeyDebug = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
  console.log(
    `🔍 SUPABASE_URL valor:`,
    supabaseUrlDebug ? `${supabaseUrlDebug.substring(0, 30)}...` : "UNDEFINED"
  );
  console.log(
    `🔍 SUPABASE_SERVICE_ROLE_KEY valor:`,
    supabaseServiceKeyDebug
      ? `${supabaseServiceKeyDebug.substring(0, 30)}...`
      : "UNDEFINED"
  );

  // Tratar requisições OPTIONS (preflight CORS)
  if (req.method === "OPTIONS") {
    console.log("✅ Requisição OPTIONS (preflight) - retornando headers CORS");
    const preflightHeaders = getPreflightHeaders(origin);
    return new Response(null, {
      status: 200,
      headers: preflightHeaders,
    });
  }

  try {
    // 🔒 SEGURANÇA: Autenticação obrigatória
    console.log("🔒 Validando autenticação do usuário...");

    const token = extractToken(req);
    if (!token) {
      console.log("❌ Token de autenticação não fornecido");
      return new Response(
        JSON.stringify({
          error: "Token de autenticação obrigatório",
        }),
        {
          status: 401,
          headers: corsHeaders,
        }
      );
    }

    const user = await validateUser(token);
    if (!user) {
      console.log("❌ Token de autenticação inválido");
      return new Response(
        JSON.stringify({
          error: "Token de autenticação inválido",
        }),
        {
          status: 401,
          headers: corsHeaders,
        }
      );
    }

    console.log("✅ Usuário autenticado com sucesso");

    // Processar e validar body da requisição
    let rawBody;
    try {
      rawBody = await req.json();
    } catch (error) {
      console.error("❌ Erro ao parsear JSON:", error);
      return new Response(
        JSON.stringify({
          error: "Body da requisição deve ser um JSON válido",
        }),
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    console.log("📝 Body recebido com sucesso");

    // Validação manual robusta
    console.log("🔍 Fazendo validação manual...");
    console.log("📝 Validando formato do body da requisição...");
    console.log("📝 Body type:", typeof rawBody);

    if (!rawBody || typeof rawBody !== "object") {
      console.log("❌ Body não é um objeto válido");
      return new Response(
        JSON.stringify({
          error: "Body deve ser um objeto JSON",
        }),
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    // Validar campos obrigatórios
    if (
      !rawBody.message ||
      typeof rawBody.message !== "string" ||
      rawBody.message.trim().length === 0
    ) {
      return new Response(
        JSON.stringify({
          error: "Mensagem é obrigatória e deve ser uma string não vazia",
        }),
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    if (!rawBody.user_id || typeof rawBody.user_id !== "string") {
      return new Response(
        JSON.stringify({
          error: "user_id é obrigatório e deve ser uma string",
        }),
        {
          status: 400,
          headers: corsHeaders,
        }
      );
    }

    const validatedData = {
      message: rawBody.message.trim(),
      obra_id: rawBody.obra_id || null,
      user_id: rawBody.user_id,
      context: rawBody.context || "geral",
      incluir_sinapi: Boolean(rawBody.incluir_sinapi),
      max_tokens: Number(rawBody.max_tokens) || 1000,
      temperatura: Number(rawBody.temperatura) || 0.7,
    };

    console.log("✅ Dados validados manualmente:", validatedData);

    // ✅ VERIFICAÇÃO DE QUOTA DE IA
    console.log(
      "🔍 Verificando quota de IA para usuário:",
      validatedData.user_id
    );
    const quotaResult = await checkAndIncrementAIUsage(
      supabaseUrl,
      supabaseServiceKey,
      validatedData.user_id,
      "chat"
    );

    if (!quotaResult.allowed) {
      console.log(
        `❌ Quota de chat IA excedida para usuário ${validatedData.user_id}`
      );
      return new Response(
        JSON.stringify({
          success: false,
          error:
            quotaResult.quotaResult.message || "Limite de uso de IA atingido",
          quota_info: {
            current: quotaResult.quotaResult.current,
            limit: quotaResult.quotaResult.limit,
            remaining: quotaResult.quotaResult.remaining,
          },
        }),
        {
          status: 429, // Too Many Requests
          headers: corsHeaders,
        }
      );
    }

    console.log(
      `✅ Quota verificada - ${quotaResult.quotaResult.remaining} usos restantes`
    );

    // Buscar contexto RAG se uma obra foi especificada
    let contextoRAG = "";
    if (validatedData.obra_id) {
      console.log(
        "🔍 [RAG] Obra especificada, buscando contexto:",
        validatedData.obra_id
      );
      const supabase = createClient(supabaseUrl, supabaseServiceKey);
      contextoRAG = await buscarContextoObra(
        validatedData.obra_id,
        validatedData.message,
        validatedData.user_id, // ✅ NOVO - Passar userId para validação
        supabase
      );
      console.log(
        "✅ [RAG] Contexto da obra obtido, tamanho:",
        contextoRAG.length
      );
    } else {
      console.log("🔍 [RAG] Conversa geral, buscando dados gerais do usuário");
      const supabase = createClient(supabaseUrl, supabaseServiceKey);
      contextoRAG = await buscarDadosGerais(validatedData.user_id, supabase);
      console.log(
        "✅ [RAG] Dados gerais obtidos, tamanho:",
        contextoRAG.length
      );
    }

    // Integração com DeepSeek API
    console.log("🤖 Chamando DeepSeek API...");
    console.log(
      "🔑 DEEPSEEK_API_KEY para API:",
      Deno.env.get("DEEPSEEK_API_KEY")
        ? `${Deno.env.get("DEEPSEEK_API_KEY")?.substring(0, 20)}...`
        : "UNDEFINED"
    );
    const startTime = Date.now();
    let chatMessage;

    try {
      // Construir prompt com contexto RAG
      let systemPrompt = `Você é o ObrasAI, um assistente especializado em construção civil e gerenciamento de obras.
Responda de forma técnica e prática, focando em soluções para construção civil.
Contexto da conversa: ${validatedData.context}
${
  validatedData.incluir_sinapi
    ? "Inclua referências SINAPI quando relevante."
    : ""
}`;

      let userPrompt = validatedData.message;

      // Se há contexto RAG, incluir na pergunta
      if (contextoRAG && validatedData.obra_id) {
        systemPrompt += `\n\nVocê tem acesso aos seguintes dados específicos da obra em questão. Use essas informações para dar respostas precisas e contextualizadas:\n\n${contextoRAG}`;
        userPrompt = `Com base nos dados da obra fornecidos, responda: ${validatedData.message}`;
        console.log("✅ [RAG] Contexto incluído no prompt");
      }

      const deepseekResponse = await fetch(
        "https://api.deepseek.com/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Deno.env.get("DEEPSEEK_API_KEY")}`,
          },
          body: JSON.stringify({
            model: "deepseek-chat",
            messages: [
              {
                role: "system",
                content: systemPrompt,
              },
              {
                role: "user",
                content: userPrompt,
              },
            ],
            max_tokens: validatedData.max_tokens,
            temperature: validatedData.temperatura,
            stream: false,
          }),
        }
      );

      if (!deepseekResponse.ok) {
        throw new Error(`DeepSeek API error: ${deepseekResponse.status}`);
      }

      const deepseekData = await deepseekResponse.json();
      const aiResponse =
        deepseekData.choices[0]?.message?.content ||
        "Desculpe, não consegui gerar uma resposta.";
      const tokensUsed = deepseekData.usage?.total_tokens || 0;

      const endTime = Date.now();
      const responseTime = (endTime - startTime) / 1000;

      console.log("✅ Resposta da DeepSeek recebida:", {
        tokens: tokensUsed,
        tempo: responseTime,
        tamanho: aiResponse.length,
      });

      chatMessage = {
        id: crypto.randomUUID(),
        usuario_id: user.id,
        obra_id: validatedData.obra_id,
        mensagem_usuario: validatedData.message,
        resposta_bot: aiResponse,
        timestamp: new Date().toISOString(),
        tokens_utilizados: tokensUsed,
        tempo_resposta: responseTime,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ Erro na DeepSeek API:", error);

      // Fallback response
      chatMessage = {
        id: crypto.randomUUID(),
        usuario_id: user.id,
        obra_id: validatedData.obra_id,
        mensagem_usuario: validatedData.message,
        resposta_bot: `Olá! Sou o ObrasAI v2.5 🏗️\n\nRecebido: "${validatedData.message}"\n\n⚠️ Modo fallback ativo (DeepSeek temporariamente indisponível).\n\nEm breve estarei conectado à IA para fornecer respostas especializadas em construção civil!`,
        timestamp: new Date().toISOString(),
        tokens_utilizados: 0,
        tempo_resposta: (Date.now() - startTime) / 1000,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    }

    console.log("✅ Resposta enviada com sucesso ao usuário");
    console.log(
      "📤 Enviando resposta final:",
      JSON.stringify({ result: chatMessage }, null, 2)
    );

    return new Response(
      JSON.stringify({
        result: chatMessage,
      }),
      {
        status: 200,
        headers: corsHeaders,
      }
    );
  } catch (error) {
    console.error("❌ Erro na Edge Function:", error);

    return new Response(
      JSON.stringify({
        error: "Erro interno do servidor",
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: corsHeaders,
      }
    );
  }
});
